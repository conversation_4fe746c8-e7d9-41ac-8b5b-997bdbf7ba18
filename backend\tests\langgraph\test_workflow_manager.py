"""
Comprehensive tests for LangGraph WorkflowManager.

This module provides comprehensive unit tests for the LangGraph WorkflowManager
including workflow creation, execution, state management, and error handling.
"""

import pytest
import asyncio
import uuid
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime
from typing import Dict, Any

# Import LangGraph components
from agents.langgraph.core.workflow_manager import WorkflowManager
from agents.langgraph.states.unified_state import (
    UnifiedDatageniusState, 
    create_unified_state,
    WorkflowStatus
)
from agents.langgraph.core.graph_builder import GraphBuilder
from agents.langgraph.core.checkpointer import StateCheckpointer


class TestWorkflowManager:
    """Comprehensive tests for LangGraph WorkflowManager."""

    @pytest.fixture
    def mock_graph_builder(self):
        """Create a mock graph builder."""
        mock_builder = Mock(spec=GraphBuilder)
        mock_builder.build_workflow_graph = AsyncMock()
        return mock_builder

    @pytest.fixture
    def mock_checkpointer(self):
        """Create a mock state checkpointer."""
        mock_checkpointer = Mock(spec=StateCheckpointer)
        mock_checkpointer.save_state = AsyncMock()
        mock_checkpointer.load_state = AsyncMock()
        mock_checkpointer.list_states = AsyncMock(return_value=[])
        return mock_checkpointer

    @pytest.fixture
    def workflow_manager(self, mock_graph_builder, mock_checkpointer):
        """Create a WorkflowManager instance with mocked dependencies."""
        manager = WorkflowManager()
        manager.graph_builder = mock_graph_builder
        manager.checkpointer = mock_checkpointer
        return manager

    @pytest.fixture
    def sample_state(self):
        """Create a sample unified state for testing."""
        return create_unified_state(
            user_id="test_user_123",
            conversation_id="conv_456",
            workflow_type="chat_message",
            initial_message={
                "content": "Hello, I need help with marketing analysis",
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
        )

    @pytest.mark.asyncio
    async def test_workflow_manager_initialization(self):
        """Test WorkflowManager initialization."""
        manager = WorkflowManager()
        
        assert manager is not None
        assert hasattr(manager, 'graph_builder')
        assert hasattr(manager, 'checkpointer')
        assert hasattr(manager, 'workflow_cache')
        assert hasattr(manager, 'metrics')
        assert isinstance(manager.workflow_cache, dict)
        assert isinstance(manager.metrics, dict)

    @pytest.mark.asyncio
    async def test_execute_workflow_success(self, workflow_manager, sample_state):
        """Test successful workflow execution."""
        # Setup mocks
        workflow_manager.graph_builder.build_workflow_graph.return_value = MagicMock()
        workflow_manager.checkpointer.save_state.return_value = True
        
        # Mock the routing and agent execution
        with patch.object(workflow_manager, '_route_to_agent', return_value="marketing_agent"):
            with patch.object(workflow_manager, '_execute_agent_node', return_value=sample_state):
                result = await workflow_manager.execute_workflow(sample_state["workflow_id"])
                
                assert result is not None
                assert "workflow_id" in result
                assert result["workflow_id"] == sample_state["workflow_id"]
                
                # Verify graph builder was called
                workflow_manager.graph_builder.build_workflow_graph.assert_called_once()
                
                # Verify state was saved
                workflow_manager.checkpointer.save_state.assert_called()

    @pytest.mark.asyncio
    async def test_execute_workflow_with_invalid_id(self, workflow_manager):
        """Test workflow execution with invalid workflow ID."""
        invalid_id = "invalid_workflow_id"
        
        result = await workflow_manager.execute_workflow(invalid_id)
        
        assert result is None

    @pytest.mark.asyncio
    async def test_execute_workflow_streaming(self, workflow_manager, sample_state):
        """Test streaming workflow execution."""
        # Setup mocks
        workflow_manager.graph_builder.build_workflow_graph.return_value = MagicMock()
        workflow_manager.checkpointer.save_state.return_value = True
        
        with patch.object(workflow_manager, '_route_to_agent', return_value="marketing_agent"):
            stream_results = []
            async for chunk in workflow_manager.execute_workflow_streaming(
                state=sample_state,
                workflow_type="chat_message"
            ):
                stream_results.append(chunk)
            
            assert len(stream_results) > 0
            
            # Check for expected streaming events
            event_types = [chunk["type"] for chunk in stream_results]
            assert "workflow_started" in event_types
            assert "workflow_completed" in event_types

    @pytest.mark.asyncio
    async def test_route_to_agent(self, workflow_manager, sample_state):
        """Test agent routing logic."""
        # Test marketing-related message
        marketing_state = sample_state.copy()
        marketing_state["messages"] = [{
            "content": "I need help with marketing strategy",
            "type": "user"
        }]
        
        agent = workflow_manager._route_to_agent(marketing_state)
        assert agent == "marketing_agent"
        
        # Test analysis-related message
        analysis_state = sample_state.copy()
        analysis_state["messages"] = [{
            "content": "Can you analyze this data for me?",
            "type": "user"
        }]
        
        agent = workflow_manager._route_to_agent(analysis_state)
        assert agent == "analysis_agent"
        
        # Test general message (should route to concierge)
        general_state = sample_state.copy()
        general_state["messages"] = [{
            "content": "Hello, how are you?",
            "type": "user"
        }]
        
        agent = workflow_manager._route_to_agent(general_state)
        assert agent == "concierge_agent"

    @pytest.mark.asyncio
    async def test_workflow_caching(self, workflow_manager, sample_state):
        """Test workflow caching functionality."""
        workflow_id = sample_state["workflow_id"]
        
        # Setup mocks
        mock_graph = MagicMock()
        workflow_manager.graph_builder.build_workflow_graph.return_value = mock_graph
        workflow_manager.checkpointer.save_state.return_value = True
        
        with patch.object(workflow_manager, '_route_to_agent', return_value="concierge_agent"):
            with patch.object(workflow_manager, '_execute_agent_node', return_value=sample_state):
                # First execution should build graph
                await workflow_manager.execute_workflow(workflow_id)
                assert workflow_id in workflow_manager.workflow_cache
                
                # Second execution should use cached graph
                workflow_manager.graph_builder.build_workflow_graph.reset_mock()
                await workflow_manager.execute_workflow(workflow_id)
                
                # Graph builder should not be called again
                workflow_manager.graph_builder.build_workflow_graph.assert_not_called()

    @pytest.mark.asyncio
    async def test_error_handling(self, workflow_manager, sample_state):
        """Test error handling in workflow execution."""
        # Setup mock to raise exception
        workflow_manager.graph_builder.build_workflow_graph.side_effect = Exception("Test error")
        
        result = await workflow_manager.execute_workflow(sample_state["workflow_id"])
        
        # Should return None on error
        assert result is None
        
        # Check that error was logged (would need to check logs in real implementation)
        assert sample_state["workflow_id"] not in workflow_manager.workflow_cache

    @pytest.mark.asyncio
    async def test_metrics_collection(self, workflow_manager, sample_state):
        """Test metrics collection during workflow execution."""
        # Setup mocks
        workflow_manager.graph_builder.build_workflow_graph.return_value = MagicMock()
        workflow_manager.checkpointer.save_state.return_value = True
        
        with patch.object(workflow_manager, '_route_to_agent', return_value="concierge_agent"):
            with patch.object(workflow_manager, '_execute_agent_node', return_value=sample_state):
                initial_metrics = workflow_manager.get_metrics().copy()
                
                await workflow_manager.execute_workflow(sample_state["workflow_id"])
                
                updated_metrics = workflow_manager.get_metrics()
                
                # Verify metrics were updated
                assert updated_metrics["total_workflows"] >= initial_metrics.get("total_workflows", 0)

    @pytest.mark.asyncio
    async def test_state_persistence(self, workflow_manager, sample_state):
        """Test state persistence through checkpointer."""
        workflow_id = sample_state["workflow_id"]
        
        # Setup mocks
        workflow_manager.graph_builder.build_workflow_graph.return_value = MagicMock()
        workflow_manager.checkpointer.save_state.return_value = True
        workflow_manager.checkpointer.load_state.return_value = sample_state
        
        with patch.object(workflow_manager, '_route_to_agent', return_value="concierge_agent"):
            with patch.object(workflow_manager, '_execute_agent_node', return_value=sample_state):
                # Execute workflow
                await workflow_manager.execute_workflow(workflow_id)
                
                # Verify state was saved
                workflow_manager.checkpointer.save_state.assert_called()
                
                # Test state loading
                loaded_state = await workflow_manager.checkpointer.load_state(workflow_id)
                assert loaded_state == sample_state

    @pytest.mark.asyncio
    async def test_concurrent_workflow_execution(self, workflow_manager):
        """Test concurrent execution of multiple workflows."""
        # Create multiple states
        states = []
        for i in range(3):
            state = create_unified_state(
                user_id=f"user_{i}",
                conversation_id=f"conv_{i}",
                workflow_type="chat_message",
                initial_message={
                    "content": f"Message {i}",
                    "type": "user",
                    "timestamp": datetime.now().isoformat()
                }
            )
            states.append(state)
        
        # Setup mocks
        workflow_manager.graph_builder.build_workflow_graph.return_value = MagicMock()
        workflow_manager.checkpointer.save_state.return_value = True
        
        with patch.object(workflow_manager, '_route_to_agent', return_value="concierge_agent"):
            with patch.object(workflow_manager, '_execute_agent_node', side_effect=states):
                # Execute workflows concurrently
                tasks = [
                    workflow_manager.execute_workflow(state["workflow_id"])
                    for state in states
                ]
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # All workflows should complete successfully
                assert len(results) == 3
                for result in results:
                    assert not isinstance(result, Exception)
                    assert result is not None

    def test_get_metrics(self, workflow_manager):
        """Test metrics retrieval."""
        metrics = workflow_manager.get_metrics()
        
        assert isinstance(metrics, dict)
        # Should contain basic metrics structure
        expected_keys = ["total_workflows", "successful_workflows", "failed_workflows"]
        for key in expected_keys:
            assert key in metrics or metrics.get(key) is not None
