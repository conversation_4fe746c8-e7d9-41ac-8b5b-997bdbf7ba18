"""
Integration tests for real-time communication in LangGraph system.

This module tests WebSocket communication, streaming responses,
and real-time collaboration features.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime
from typing import Dict, Any, List

# Import components for real-time testing
from agents.langgraph.api.websocket_manager import WebSocketManager
from agents.langgraph.core.workflow_manager import WorkflowManager
from agents.langgraph.states.unified_state import create_unified_state


class MockWebSocket:
    """Mock WebSocket for testing."""
    
    def __init__(self):
        self.sent_messages = []
        self.closed = False
        self.close_code = None
        
    async def send_text(self, message: str):
        """Mock send_text method."""
        self.sent_messages.append(json.loads(message))
        
    async def close(self, code: int = 1000):
        """Mock close method."""
        self.closed = True
        self.close_code = code


class TestRealTimeCommunication:
    """Integration tests for real-time communication."""

    @pytest.fixture
    def mock_websocket(self):
        """Create a mock WebSocket."""
        return MockWebSocket()

    @pytest.fixture
    def websocket_manager(self):
        """Create a WebSocket manager."""
        return WebSocketManager()

    @pytest.fixture
    def workflow_manager(self):
        """Create a workflow manager."""
        return WorkflowManager()

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_websocket_connection_lifecycle(self, websocket_manager, mock_websocket):
        """Test WebSocket connection lifecycle."""
        conversation_id = "test_conv_123"
        user_id = "test_user_456"
        
        # Test connection
        await websocket_manager.connect(mock_websocket, conversation_id, user_id)
        
        # Verify connection was established
        assert conversation_id in websocket_manager.active_connections
        assert mock_websocket in websocket_manager.active_connections[conversation_id]
        
        # Test disconnection
        await websocket_manager.disconnect(mock_websocket, conversation_id)
        
        # Verify disconnection
        if conversation_id in websocket_manager.active_connections:
            assert mock_websocket not in websocket_manager.active_connections[conversation_id]

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_message_broadcasting(self, websocket_manager, mock_websocket):
        """Test message broadcasting to connected clients."""
        conversation_id = "test_conv_123"
        user_id = "test_user_456"
        
        # Connect WebSocket
        await websocket_manager.connect(mock_websocket, conversation_id, user_id)
        
        # Broadcast message
        test_message = {
            "type": "agent_response",
            "content": "Hello from agent",
            "timestamp": datetime.now().isoformat()
        }
        
        await websocket_manager.broadcast(test_message, conversation_id)
        
        # Verify message was sent
        assert len(mock_websocket.sent_messages) == 1
        sent_message = mock_websocket.sent_messages[0]
        assert sent_message["type"] == "agent_response"
        assert sent_message["content"] == "Hello from agent"

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_streaming_response_integration(self, websocket_manager, workflow_manager, mock_websocket):
        """Test streaming response integration."""
        conversation_id = "test_conv_123"
        user_id = "test_user_456"
        
        # Connect WebSocket
        await websocket_manager.connect(mock_websocket, conversation_id, user_id)
        
        # Create initial state for streaming
        initial_state = create_unified_state(
            user_id=user_id,
            conversation_id=conversation_id,
            workflow_type="streaming_test",
            initial_message={
                "content": "Generate a streaming response",
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
        )
        
        # Mock streaming workflow execution
        async def mock_streaming_generator():
            """Mock streaming generator."""
            events = [
                {"type": "workflow_started", "timestamp": datetime.now().isoformat()},
                {"type": "content", "content": "Hello ", "timestamp": datetime.now().isoformat()},
                {"type": "content", "content": "from ", "timestamp": datetime.now().isoformat()},
                {"type": "content", "content": "streaming ", "timestamp": datetime.now().isoformat()},
                {"type": "content", "content": "agent!", "timestamp": datetime.now().isoformat()},
                {"type": "workflow_completed", "timestamp": datetime.now().isoformat()}
            ]
            
            for event in events:
                yield event
                await asyncio.sleep(0.01)  # Small delay to simulate real streaming
        
        with patch.object(workflow_manager, 'execute_workflow_streaming', return_value=mock_streaming_generator()):
            # Process streaming workflow
            stream_count = 0
            async for event in workflow_manager.execute_workflow_streaming(
                state=initial_state,
                workflow_type="streaming_test"
            ):
                # Broadcast each streaming event
                await websocket_manager.broadcast(event, conversation_id)
                stream_count += 1
                
                if stream_count >= 6:  # Expected number of events
                    break
        
        # Verify streaming messages were sent
        assert len(mock_websocket.sent_messages) >= 6
        
        # Verify message types
        message_types = [msg["type"] for msg in mock_websocket.sent_messages]
        assert "workflow_started" in message_types
        assert "content" in message_types
        assert "workflow_completed" in message_types
        
        # Verify content messages
        content_messages = [msg for msg in mock_websocket.sent_messages if msg["type"] == "content"]
        assert len(content_messages) == 4
        
        # Reconstruct streamed content
        full_content = "".join(msg["content"] for msg in content_messages)
        assert full_content == "Hello from streaming agent!"

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_typing_indicator_integration(self, websocket_manager, mock_websocket):
        """Test typing indicator functionality."""
        conversation_id = "test_conv_123"
        user_id = "test_user_456"
        
        # Connect WebSocket
        await websocket_manager.connect(mock_websocket, conversation_id, user_id)
        
        # Send typing indicator
        await websocket_manager.send_typing_indicator(conversation_id, True)
        
        # Verify typing indicator was sent
        assert len(mock_websocket.sent_messages) == 1
        typing_message = mock_websocket.sent_messages[0]
        assert typing_message["type"] == "typing_indicator"
        assert typing_message["typing"] is True
        
        # Stop typing indicator
        await websocket_manager.send_typing_indicator(conversation_id, False)
        
        # Verify stop typing was sent
        assert len(mock_websocket.sent_messages) == 2
        stop_typing_message = mock_websocket.sent_messages[1]
        assert stop_typing_message["type"] == "typing_indicator"
        assert stop_typing_message["typing"] is False

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_multi_client_broadcasting(self, websocket_manager):
        """Test broadcasting to multiple connected clients."""
        conversation_id = "test_conv_123"
        
        # Create multiple mock WebSockets
        websockets = [MockWebSocket() for _ in range(3)]
        user_ids = [f"user_{i}" for i in range(3)]
        
        # Connect all WebSockets
        for ws, user_id in zip(websockets, user_ids):
            await websocket_manager.connect(ws, conversation_id, user_id)
        
        # Broadcast message
        test_message = {
            "type": "broadcast_test",
            "content": "Message to all clients",
            "timestamp": datetime.now().isoformat()
        }
        
        await websocket_manager.broadcast(test_message, conversation_id)
        
        # Verify all clients received the message
        for ws in websockets:
            assert len(ws.sent_messages) == 1
            assert ws.sent_messages[0]["type"] == "broadcast_test"
            assert ws.sent_messages[0]["content"] == "Message to all clients"

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_agent_status_updates(self, websocket_manager, mock_websocket):
        """Test agent status updates via WebSocket."""
        conversation_id = "test_conv_123"
        user_id = "test_user_456"
        
        # Connect WebSocket
        await websocket_manager.connect(mock_websocket, conversation_id, user_id)
        
        # Send agent status updates
        status_updates = [
            {"type": "agent_status", "agent": "concierge_agent", "status": "thinking"},
            {"type": "agent_status", "agent": "concierge_agent", "status": "routing"},
            {"type": "agent_status", "agent": "marketing_agent", "status": "active"},
            {"type": "agent_status", "agent": "marketing_agent", "status": "generating"},
            {"type": "agent_status", "agent": "marketing_agent", "status": "complete"}
        ]
        
        for update in status_updates:
            await websocket_manager.broadcast(update, conversation_id)
        
        # Verify all status updates were sent
        assert len(mock_websocket.sent_messages) == 5
        
        # Verify status progression
        statuses = [msg["status"] for msg in mock_websocket.sent_messages]
        assert statuses == ["thinking", "routing", "active", "generating", "complete"]

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_error_handling_in_realtime_communication(self, websocket_manager, mock_websocket):
        """Test error handling in real-time communication."""
        conversation_id = "test_conv_123"
        user_id = "test_user_456"
        
        # Connect WebSocket
        await websocket_manager.connect(mock_websocket, conversation_id, user_id)
        
        # Mock WebSocket send_text to raise an exception
        original_send_text = mock_websocket.send_text
        mock_websocket.send_text = AsyncMock(side_effect=Exception("Connection lost"))
        
        # Try to broadcast message (should handle error gracefully)
        test_message = {"type": "error_test", "content": "Test message"}
        
        # This should not raise an exception
        await websocket_manager.broadcast(test_message, conversation_id)
        
        # Verify WebSocket was removed from active connections due to error
        if conversation_id in websocket_manager.active_connections:
            assert mock_websocket not in websocket_manager.active_connections[conversation_id]

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_concurrent_streaming_sessions(self, websocket_manager, workflow_manager):
        """Test concurrent streaming sessions."""
        # Create multiple WebSocket connections
        connections = []
        for i in range(3):
            mock_ws = MockWebSocket()
            conv_id = f"test_conv_{i}"
            user_id = f"test_user_{i}"
            
            await websocket_manager.connect(mock_ws, conv_id, user_id)
            connections.append((mock_ws, conv_id, user_id))
        
        # Create streaming generators for each session
        async def create_streaming_generator(session_id):
            """Create a streaming generator for a session."""
            events = [
                {"type": "session_start", "session_id": session_id},
                {"type": "content", "content": f"Content from session {session_id}"},
                {"type": "session_end", "session_id": session_id}
            ]
            
            for event in events:
                yield event
                await asyncio.sleep(0.01)
        
        # Start concurrent streaming sessions
        streaming_tasks = []
        for i, (mock_ws, conv_id, user_id) in enumerate(connections):
            async def stream_to_client(ws, conversation_id, session_id):
                async for event in create_streaming_generator(session_id):
                    await websocket_manager.broadcast(event, conversation_id)
            
            task = asyncio.create_task(stream_to_client(mock_ws, conv_id, i))
            streaming_tasks.append(task)
        
        # Wait for all streaming sessions to complete
        await asyncio.gather(*streaming_tasks)
        
        # Verify each client received their session's messages
        for i, (mock_ws, _, _) in enumerate(connections):
            assert len(mock_ws.sent_messages) == 3
            
            # Verify session-specific content
            content_msg = next(msg for msg in mock_ws.sent_messages if msg["type"] == "content")
            assert f"session {i}" in content_msg["content"]

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_websocket_heartbeat_and_keepalive(self, websocket_manager, mock_websocket):
        """Test WebSocket heartbeat and keep-alive functionality."""
        conversation_id = "test_conv_123"
        user_id = "test_user_456"
        
        # Connect WebSocket
        await websocket_manager.connect(mock_websocket, conversation_id, user_id)
        
        # Mock heartbeat functionality
        with patch.object(websocket_manager, 'send_heartbeat') as mock_heartbeat:
            # Simulate heartbeat
            await websocket_manager.send_heartbeat(conversation_id)
            
            # Verify heartbeat was called
            mock_heartbeat.assert_called_once_with(conversation_id)

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_message_queuing_and_delivery(self, websocket_manager):
        """Test message queuing when clients are temporarily disconnected."""
        conversation_id = "test_conv_123"
        user_id = "test_user_456"
        mock_websocket = MockWebSocket()
        
        # Connect and then disconnect
        await websocket_manager.connect(mock_websocket, conversation_id, user_id)
        await websocket_manager.disconnect(mock_websocket, conversation_id)
        
        # Try to broadcast message to disconnected client
        test_message = {"type": "queued_message", "content": "This should be queued"}
        await websocket_manager.broadcast(test_message, conversation_id)
        
        # Reconnect
        new_mock_websocket = MockWebSocket()
        await websocket_manager.connect(new_mock_websocket, conversation_id, user_id)
        
        # In a real implementation, queued messages would be delivered here
        # For this test, we just verify the connection works
        assert conversation_id in websocket_manager.active_connections

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_realtime_collaboration_updates(self, websocket_manager):
        """Test real-time collaboration updates between agents."""
        conversation_id = "test_conv_123"
        
        # Create multiple WebSocket connections (simulating different agent views)
        websockets = []
        for i in range(2):
            mock_ws = MockWebSocket()
            user_id = f"collaborator_{i}"
            await websocket_manager.connect(mock_ws, conversation_id, user_id)
            websockets.append(mock_ws)
        
        # Simulate collaboration events
        collaboration_events = [
            {"type": "agent_joined", "agent": "marketing_agent", "timestamp": datetime.now().isoformat()},
            {"type": "insight_shared", "from": "analysis_agent", "to": "marketing_agent", "content": "Key insight"},
            {"type": "consensus_reached", "decision": "marketing_strategy", "result": "approved"},
            {"type": "workflow_completed", "result": "success"}
        ]
        
        # Broadcast collaboration events
        for event in collaboration_events:
            await websocket_manager.broadcast(event, conversation_id)
        
        # Verify all collaborators received all events
        for ws in websockets:
            assert len(ws.sent_messages) == 4
            
            # Verify event types
            event_types = [msg["type"] for msg in ws.sent_messages]
            assert "agent_joined" in event_types
            assert "insight_shared" in event_types
            assert "consensus_reached" in event_types
            assert "workflow_completed" in event_types
