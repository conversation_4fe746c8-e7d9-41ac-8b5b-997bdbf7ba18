"""
Pytest configuration and fixtures for LangGraph tests.

This module provides common fixtures and configuration for all LangGraph tests.
"""

import pytest
import asyncio
import tempfile
import os
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

# Import LangGraph components for fixtures
from agents.langgraph.states.unified_state import create_unified_state
from agents.langgraph.core.workflow_manager import WorkflowManager
from agents.langgraph.intelligence import CrossAgentIntelligenceService


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_database():
    """Create a mock database session."""
    mock_db = Mock()
    mock_db.commit = Mock()
    mock_db.rollback = Mock()
    mock_db.close = Mock()
    mock_db.query = Mock()
    mock_db.add = Mock()
    mock_db.delete = Mock()
    return mock_db


@pytest.fixture
def temp_directory():
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def sample_user_data():
    """Create sample user data for testing."""
    return {
        "user_id": "test_user_123",
        "email": "<EMAIL>",
        "name": "Test User",
        "created_at": datetime.now().isoformat()
    }


@pytest.fixture
def sample_conversation_data():
    """Create sample conversation data for testing."""
    return {
        "conversation_id": "conv_456",
        "user_id": "test_user_123",
        "persona_id": "concierge_agent",
        "title": "Test Conversation",
        "created_at": datetime.now().isoformat()
    }


@pytest.fixture
def sample_business_profile():
    """Create sample business profile data for testing."""
    return {
        "id": "profile_123",
        "business_name": "Test Business Inc.",
        "industry": "Technology",
        "business_description": "A test technology company",
        "business_size": "small",
        "target_audience": "Tech professionals",
        "products_services": "Software solutions",
        "marketing_goals": ["increase_awareness", "generate_leads"],
        "created_at": datetime.now().isoformat()
    }


@pytest.fixture
def unified_state_factory():
    """Factory function for creating unified states."""
    def _create_state(
        user_id="test_user",
        conversation_id="test_conv",
        workflow_type="test_workflow",
        **kwargs
    ):
        return create_unified_state(
            user_id=user_id,
            conversation_id=conversation_id,
            workflow_type=workflow_type,
            **kwargs
        )
    return _create_state


@pytest.fixture
def mock_workflow_manager():
    """Create a mock workflow manager."""
    mock_manager = Mock(spec=WorkflowManager)
    mock_manager.execute_workflow = AsyncMock()
    mock_manager.execute_workflow_streaming = AsyncMock()
    mock_manager.get_metrics = Mock(return_value={})
    return mock_manager


@pytest.fixture
def mock_intelligence_service():
    """Create a mock cross-agent intelligence service."""
    mock_service = Mock(spec=CrossAgentIntelligenceService)
    mock_service.share_insight = AsyncMock()
    mock_service.get_agent_insights = AsyncMock(return_value=[])
    mock_service.initiate_collaboration = AsyncMock()
    mock_service.load_business_context = AsyncMock()
    return mock_service


@pytest.fixture
def mock_agent_factory():
    """Create a mock agent factory."""
    mock_factory = Mock()
    mock_factory.create_agent_node = Mock()
    mock_factory.get_agent_instance = Mock()
    mock_factory.get_available_agents = Mock(return_value=[
        "concierge_agent", "marketing_agent", "analysis_agent", "classification_agent"
    ])
    mock_factory.get_agent_config = Mock(return_value={})
    return mock_factory


@pytest.fixture
def mock_mcp_tools():
    """Create mock MCP tools."""
    mock_tools = {
        "text_classification": AsyncMock(return_value={
            "isError": False,
            "content": [{"text": "Classification completed"}],
            "metadata": {"results": [{"category": "test", "confidence": 0.9}]}
        }),
        "data_analysis": AsyncMock(return_value={
            "isError": False,
            "content": [{"text": "Analysis completed"}],
            "metadata": {"results": {"insights": ["test insight"]}}
        }),
        "web_research": AsyncMock(return_value={
            "isError": False,
            "content": [{"text": "Research completed"}],
            "metadata": {"results": {"findings": ["test finding"]}}
        })
    }
    return mock_tools


@pytest.fixture(autouse=True)
def mock_environment_variables():
    """Mock environment variables for testing."""
    with patch.dict(os.environ, {
        "DATABASE_URL": "sqlite:///test.db",
        "SECRET_KEY": "test_secret_key",
        "GROQ_API_KEY": "test_groq_key",
        "OPENAI_API_KEY": "test_openai_key",
        "GOOGLE_API_KEY": "test_google_key",
        "TESTING": "true"
    }):
        yield


@pytest.fixture
def mock_logger():
    """Create a mock logger."""
    mock_logger = Mock()
    mock_logger.info = Mock()
    mock_logger.debug = Mock()
    mock_logger.warning = Mock()
    mock_logger.error = Mock()
    mock_logger.critical = Mock()
    return mock_logger


# Async test helpers
@pytest.fixture
def async_test_timeout():
    """Default timeout for async tests."""
    return 30  # seconds


def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "asyncio: mark test as async"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "unit: mark test as unit test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add asyncio marker to async tests
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.asyncio)
        
        # Add unit marker to tests in unit test directories
        if "test_" in item.nodeid and "integration" not in item.nodeid:
            item.add_marker(pytest.mark.unit)


# Performance testing fixtures
@pytest.fixture
def performance_timer():
    """Timer fixture for performance testing."""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()


@pytest.fixture
def memory_profiler():
    """Memory profiler fixture for memory usage testing."""
    import psutil
    import os
    
    class MemoryProfiler:
        def __init__(self):
            self.process = psutil.Process(os.getpid())
            self.initial_memory = None
            self.peak_memory = None
        
        def start(self):
            self.initial_memory = self.process.memory_info().rss
            self.peak_memory = self.initial_memory
        
        def update(self):
            current_memory = self.process.memory_info().rss
            if current_memory > self.peak_memory:
                self.peak_memory = current_memory
        
        @property
        def memory_increase(self):
            if self.initial_memory and self.peak_memory:
                return self.peak_memory - self.initial_memory
            return None
    
    return MemoryProfiler()
