#!/usr/bin/env python3
"""
Test script for the unified agent system.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_agent_factory():
    """Test the agent factory and unified agent system."""
    try:
        from agents.langgraph.core.agent_factory import AgentNodeFactory
        
        print("✅ Successfully imported AgentNodeFactory")
        
        # Initialize factory
        factory = AgentNodeFactory()
        print("✅ Successfully initialized AgentNodeFactory")
        
        # Get available agents
        available_agents = factory.get_available_agents()
        print(f"✅ Available agents: {available_agents}")
        
        # Test creating a concierge agent
        if "concierge-agent" in available_agents:
            agent = factory.create_agent_node("concierge-agent")
            if agent:
                print(f"✅ Successfully created concierge agent: {agent.agent_id}")
                print(f"   Agent type: {agent.agent_type}")
                print(f"   Capabilities: {agent.capabilities[:3]}...")  # Show first 3
                print(f"   Supported intents: {agent.supported_intents[:3]}...")  # Show first 3
            else:
                print("❌ Failed to create concierge agent")
        else:
            print("❌ Concierge agent not found in available agents")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing agent factory: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_routing_node():
    """Test the routing node configuration loading."""
    try:
        from agents.langgraph.nodes.routing_node import RoutingNode
        from agents.langgraph.nodes.base_agent_node import UnifiedAgentNode
        
        print("✅ Successfully imported RoutingNode and UnifiedAgentNode")
        
        # Create a mock agent for testing
        mock_agents = {
            "concierge-agent": UnifiedAgentNode("concierge-agent", {
                "agent_type": "concierge",
                "agent_class": "agents.concierge_agent.concierge.ConciergeAgent"
            })
        }
        
        # Initialize routing node
        routing_node = RoutingNode(mock_agents)
        print("✅ Successfully initialized RoutingNode")
        
        # Check if intent patterns were loaded
        if hasattr(routing_node, 'intent_patterns') and routing_node.intent_patterns:
            print(f"✅ Intent patterns loaded: {list(routing_node.intent_patterns.keys())}")
        else:
            print("❌ Intent patterns not loaded")
        
        # Check default agent
        if routing_node.default_agent:
            print(f"✅ Default agent set: {routing_node.default_agent}")
        else:
            print("❌ No default agent set")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing routing node: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Unified Agent System")
    print("=" * 50)
    
    success = True
    
    print("\n📋 Test 1: Agent Factory")
    success &= test_agent_factory()
    
    print("\n📋 Test 2: Routing Node")
    success &= test_routing_node()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! The unified agent system is working.")
    else:
        print("💥 Some tests failed. Check the errors above.")
    
    return success

if __name__ == "__main__":
    main()
