"""
Database migration script to create LangGraph-related tables.

This script creates the necessary tables for LangGraph workflow persistence
and state management.
"""

import logging
import sys
import os
from pathlib import Path

# Add the parent directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from app.database import engine
from sqlalchemy import text

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_langgraph_tables():
    """Create the LangGraph-related tables in the database."""
    logger.info("Creating LangGraph-related tables...")
    
    # SQL to create the tables
    sql_statements = [
        # LangGraph Workflow Checkpoints Table
        """
        CREATE TABLE IF NOT EXISTS langgraph_workflow_checkpoints (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            workflow_id VARCHAR(255) NOT NULL,
            checkpoint_id VARCHAR(255) NOT NULL,
            state_data JSONB NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            UNIQUE(workflow_id, checkpoint_id)
        );
        """,
        
        # Index for efficient workflow lookups
        """
        CREATE INDEX IF NOT EXISTS idx_langgraph_checkpoints_workflow_id
        ON langgraph_workflow_checkpoints(workflow_id);
        """,
        
        # Index for efficient checkpoint lookups
        """
        CREATE INDEX IF NOT EXISTS idx_langgraph_checkpoints_created_at
        ON langgraph_workflow_checkpoints(created_at DESC);
        """,
        
        # LangGraph Workflow States Table
        """
        CREATE TABLE IF NOT EXISTS langgraph_workflow_states (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            workflow_id VARCHAR(255) NOT NULL,
            checkpoint_id VARCHAR(255) NOT NULL,
            state_name VARCHAR(100) NOT NULL,
            state_type VARCHAR(50) NOT NULL,
            previous_state VARCHAR(100),
            next_state VARCHAR(100),
            started_at TIMESTAMP WITH TIME ZONE NOT NULL,
            completed_at TIMESTAMP WITH TIME ZONE,
            duration_ms INTEGER,
            input_data JSONB DEFAULT '{}',
            output_data JSONB DEFAULT '{}',
            error_data JSONB,
            agent_id VARCHAR(255),
            tool_name VARCHAR(255),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        # Workflow states indexes
        """
        CREATE INDEX IF NOT EXISTS idx_langgraph_states_workflow_id 
        ON langgraph_workflow_states(workflow_id);
        """,
        
        """
        CREATE INDEX IF NOT EXISTS idx_langgraph_states_checkpoint_id 
        ON langgraph_workflow_states(checkpoint_id);
        """,
        
        """
        CREATE INDEX IF NOT EXISTS idx_langgraph_states_agent_id 
        ON langgraph_workflow_states(agent_id);
        """,
        
        # Cross-Agent Intelligence Table
        """
        CREATE TABLE IF NOT EXISTS cross_agent_insights (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            source_agent VARCHAR(255) NOT NULL,
            source_workflow_id UUID,
            business_profile_id UUID,
            insight_type VARCHAR(100) NOT NULL,
            insight_content JSONB NOT NULL,
            insight_tags TEXT[],
            relevance_score DECIMAL(5,4) DEFAULT 1.0,
            quality_score DECIMAL(5,4) DEFAULT 1.0,
            confidence_score DECIMAL(5,4) DEFAULT 1.0,
            access_count INTEGER DEFAULT 0,
            last_accessed TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            expires_at TIMESTAMP WITH TIME ZONE
        );
        """,
        
        # Feature flags table for LangGraph migration
        """
        CREATE TABLE IF NOT EXISTS feature_flags (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            flag_name VARCHAR(255) NOT NULL UNIQUE,
            status VARCHAR(50) NOT NULL DEFAULT 'disabled',
            scope VARCHAR(50) NOT NULL DEFAULT 'global',
            description TEXT,
            rollout_percentage DECIMAL(5,2) DEFAULT 0.0,
            dependencies TEXT[],
            configuration JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            created_by VARCHAR(255),
            updated_by VARCHAR(255)
        );
        """
    ]
    
    try:
        with engine.connect() as conn:
            for sql in sql_statements:
                logger.info(f"Executing SQL: {sql.strip()[:100]}...")
                conn.execute(text(sql))
            conn.commit()
        
        logger.info("LangGraph tables created successfully.")
        return True
        
    except Exception as e:
        logger.error(f"Error creating LangGraph tables: {e}")
        return False


if __name__ == "__main__":
    success = create_langgraph_tables()
    if success:
        logger.info("Migration completed successfully.")
        sys.exit(0)
    else:
        logger.error("Migration failed.")
        sys.exit(1)
