"""
Marketplace-Integrated Agent Factory for LangGraph-based Datagenius System.

This module provides enhanced agent factory capabilities that integrate with
the AI persona marketplace, supporting dynamic agent loading from marketplace
purchases and custom third-party installations.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Type, Set
from datetime import datetime
from dataclasses import dataclass
import uuid

logger = logging.getLogger(__name__)

from .agent_factory import agent_factory, BaseAgentFactory
from ..nodes.base_agent_node import BaseAgentNode
from ..marketplace.capability_marketplace import CapabilityMarketplace
from ..plugins.plugin_manager import AgentPluginManager
from ..intelligence.cross_agent_intelligence import CrossAgentIntelligenceManager

# Import enhanced marketplace services
try:
    from ....app.services.enhanced_marketplace_service import (
        persona_config_service,
        agent_plugin_service
    )
    from ....app.database import get_db
    ENHANCED_MARKETPLACE_AVAILABLE = True
except ImportError:
    ENHANCED_MARKETPLACE_AVAILABLE = False
    logger.warning("Enhanced marketplace services not available")


@dataclass
class AgentDefinition:
    """Definition of an agent with marketplace and plugin information."""
    agent_id: str
    name: str
    description: str
    industry: str
    capabilities: List[str]
    plugin_id: Optional[str] = None
    marketplace_listing_id: Optional[str] = None
    is_purchased: bool = False
    is_custom: bool = False
    configuration: Dict[str, Any] = None


@dataclass
class UserContext:
    """User context for agent discovery and workflow creation."""
    user_id: str
    business_profile: Optional[Dict[str, Any]] = None
    purchased_personas: List[str] = None
    industry_preferences: List[str] = None
    compliance_requirements: List[str] = None


class MarketplaceClient:
    """Client for interacting with the AI persona marketplace."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def get_user_personas(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's purchased personas from marketplace."""
        try:
            # Import persona service to get purchased personas
            from ....app.services import persona_service
            from ....app.database import get_db
            
            # Get database session
            db = next(get_db())
            
            # Get purchased persona IDs
            purchased_persona_ids = persona_service.get_user_purchased_personas(db, user_id)
            
            # Get persona details
            personas = []
            for persona_id in purchased_persona_ids:
                persona_data = await persona_service.get_persona_details(db, persona_id)
                if persona_data:
                    personas.append(persona_data)
            
            self.logger.info(f"Retrieved {len(personas)} purchased personas for user {user_id}")
            return personas
            
        except Exception as e:
            self.logger.error(f"Error retrieving user personas: {e}")
            return []
    
    async def get_marketplace_listings(self, industry: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get available marketplace listings."""
        try:
            # Import personas API to get available personas
            from ....app.database import get_db, get_personas
            
            # Get database session
            db = next(get_db())
            
            # Get available personas
            db_personas = get_personas(db, skip=0, limit=100, industry=industry)
            
            # Convert to marketplace listings
            listings = []
            for persona in db_personas:
                listing = {
                    "persona_id": persona.id,
                    "name": persona.name,
                    "description": persona.description,
                    "industry": persona.industry,
                    "skills": persona.skills,
                    "price": persona.price,
                    "rating": persona.rating,
                    "is_active": persona.is_active
                }
                listings.append(listing)
            
            return listings
            
        except Exception as e:
            self.logger.error(f"Error retrieving marketplace listings: {e}")
            return []


class PersonaRegistry:
    """Registry for persona configurations and metadata."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.persona_configs: Dict[str, Dict[str, Any]] = {}
    
    async def get_persona_config(self, persona_id: str) -> Dict[str, Any]:
        """Get persona configuration."""
        try:
            # Check cache first
            if persona_id in self.persona_configs:
                return self.persona_configs[persona_id]
            
            # Load from database or file system
            config = await self._load_persona_config(persona_id)
            
            # Cache the configuration
            self.persona_configs[persona_id] = config
            
            return config
            
        except Exception as e:
            self.logger.error(f"Error getting persona config for {persona_id}: {e}")
            return {}
    
    async def _load_persona_config(self, persona_id: str) -> Dict[str, Any]:
        """Load persona configuration from storage."""
        try:
            # Try to load from YAML file first
            import yaml
            from pathlib import Path
            
            config_path = Path(f"personas/{persona_id}.yaml")
            if config_path.exists():
                with open(config_path, 'r') as f:
                    return yaml.safe_load(f)
            
            # Fallback to default configuration
            return {
                "persona_id": persona_id,
                "methodology_framework": "UNDERSTAND_ASSESS_EXECUTE_DELIVER",
                "enable_cross_agent_intelligence": True,
                "enable_business_profile_context": True,
                "specialized_tools": [],
                "compliance_requirements": [],
                "industry_specialization": None
            }
            
        except Exception as e:
            self.logger.error(f"Error loading persona config for {persona_id}: {e}")
            return {}


class CustomAgentLoader:
    """Loader for custom third-party agents."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.plugin_manager = AgentPluginManager()
    
    async def load_agent(self, agent_config: Dict[str, Any]) -> BaseAgentNode:
        """Load custom agent from configuration."""
        try:
            plugin_id = agent_config.get("plugin_id")
            if not plugin_id:
                raise ValueError("Plugin ID required for custom agent")
            
            # Load agent from plugin
            agent_node = await self.plugin_manager.load_plugin_agent(plugin_id, agent_config)
            
            self.logger.info(f"Successfully loaded custom agent from plugin {plugin_id}")
            return agent_node
            
        except Exception as e:
            self.logger.error(f"Error loading custom agent: {e}")
            raise


class MarketplaceAgentFactory(BaseAgentFactory):
    """
    Enhanced agent factory that integrates with the AI persona marketplace.
    Supports dynamic agent loading from marketplace purchases and custom installations.
    """
    
    def __init__(self):
        super().__init__()
        self.marketplace_client = MarketplaceClient()
        self.persona_registry = PersonaRegistry()
        self.custom_agent_loader = CustomAgentLoader()
        self.capability_marketplace = CapabilityMarketplace()
        self.cross_agent_intelligence = CrossAgentIntelligenceManager()
        
        # Cache for discovered agents
        self.discovered_agents: Dict[str, AgentDefinition] = {}
        self.last_discovery_time = None
        
        self.logger = logging.getLogger(__name__)

    async def initialize(self) -> None:
        """Initialize the marketplace agent factory."""
        try:
            self.logger.info("Initializing MarketplaceAgentFactory...")

            # Initialize sub-components
            if hasattr(self.capability_marketplace, 'initialize'):
                await self.capability_marketplace.initialize()

            if hasattr(self.cross_agent_intelligence, 'initialize'):
                await self.cross_agent_intelligence.initialize()

            self.logger.info("MarketplaceAgentFactory initialized successfully")
        except Exception as e:
            self.logger.error(f"Error initializing MarketplaceAgentFactory: {e}")
            raise

    async def discover_marketplace_agents(self, user_id: str) -> List[AgentDefinition]:
        """Discover agents from user's marketplace purchases."""
        try:
            self.logger.info(f"Discovering marketplace agents for user {user_id}")
            
            # Get user's purchased personas
            purchased_personas = await self.marketplace_client.get_user_personas(user_id)
            
            # Convert to agent definitions
            agent_definitions = []
            for persona in purchased_personas:
                definition = await self._create_agent_definition(persona, is_purchased=True)
                agent_definitions.append(definition)
            
            self.logger.info(f"Discovered {len(agent_definitions)} marketplace agents")
            return agent_definitions
            
        except Exception as e:
            self.logger.error(f"Error discovering marketplace agents: {e}")
            return []
    
    async def discover_available_agents(self, industry: Optional[str] = None) -> List[AgentDefinition]:
        """Discover all available agents in marketplace."""
        try:
            # Get marketplace listings
            listings = await self.marketplace_client.get_marketplace_listings(industry)
            
            # Convert to agent definitions
            agent_definitions = []
            for listing in listings:
                definition = await self._create_agent_definition(listing, is_purchased=False)
                agent_definitions.append(definition)
            
            return agent_definitions
            
        except Exception as e:
            self.logger.error(f"Error discovering available agents: {e}")
            return []
    
    async def load_custom_agent(self, agent_config: Dict[str, Any]) -> BaseAgentNode:
        """Load custom third-party agent from configuration."""
        return await self.custom_agent_loader.load_agent(agent_config)
    
    async def create_marketplace_agent(
        self, 
        persona_id: str, 
        user_context: UserContext
    ) -> BaseAgentNode:
        """Create agent instance from marketplace persona."""
        try:
            # Get persona configuration
            persona_config = await self.persona_registry.get_persona_config(persona_id)
            
            # Check if user has purchased this persona
            if not self._is_persona_purchased(persona_id, user_context):
                raise ValueError(f"User {user_context.user_id} has not purchased persona {persona_id}")
            
            # Create agent using existing factory
            agent_node = await self.create_agent(persona_id, persona_config)
            
            # Enhance with marketplace-specific features
            await self._enhance_marketplace_agent(agent_node, persona_config, user_context)
            
            return agent_node
            
        except Exception as e:
            self.logger.error(f"Error creating marketplace agent {persona_id}: {e}")
            raise
    
    async def _create_agent_definition(
        self, 
        persona_data: Dict[str, Any], 
        is_purchased: bool = False
    ) -> AgentDefinition:
        """Create agent definition from persona data."""
        return AgentDefinition(
            agent_id=persona_data.get("persona_id", persona_data.get("id")),
            name=persona_data.get("name", "Unknown Agent"),
            description=persona_data.get("description", ""),
            industry=persona_data.get("industry", "general"),
            capabilities=persona_data.get("skills", []),
            marketplace_listing_id=persona_data.get("id"),
            is_purchased=is_purchased,
            is_custom=False,
            configuration=persona_data
        )
    
    def _is_persona_purchased(self, persona_id: str, user_context: UserContext) -> bool:
        """Check if user has purchased the persona."""
        if not user_context.purchased_personas:
            return False
        return persona_id in user_context.purchased_personas
    
    async def _enhance_marketplace_agent(
        self, 
        agent_node: BaseAgentNode, 
        persona_config: Dict[str, Any],
        user_context: UserContext
    ) -> None:
        """Enhance agent with marketplace-specific features."""
        try:
            # Add cross-agent intelligence if enabled
            if persona_config.get("enable_cross_agent_intelligence", True):
                await self.cross_agent_intelligence.register_agent(
                    agent_node.agent_id, 
                    user_context.business_profile
                )
            
            # Add marketplace capabilities
            if persona_config.get("marketplace_capabilities"):
                await self.capability_marketplace.register_agent_capabilities(
                    agent_node.agent_id,
                    persona_config["marketplace_capabilities"]
                )
            
            self.logger.info(f"Enhanced marketplace agent {agent_node.agent_id}")
            
        except Exception as e:
            self.logger.error(f"Error enhancing marketplace agent: {e}")


# Global instance
marketplace_agent_factory = MarketplaceAgentFactory()
