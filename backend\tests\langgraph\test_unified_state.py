"""
Comprehensive tests for LangGraph Unified State Management.

This module provides comprehensive unit tests for the unified state system
including state creation, updates, cross-agent intelligence, and persistence.
"""

import pytest
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any
from unittest.mock import Mock, patch

# Import unified state components
from agents.langgraph.states.unified_state import (
    UnifiedDatageniusState,
    WorkflowStatus,
    AgentRole,
    create_unified_state,
    update_workflow_status,
    add_cross_agent_insight,
    get_relevant_insights,
    build_agent_consensus,
    record_agent_vote,
    load_business_profile_context,
    update_agent_transition
)


class TestUnifiedState:
    """Comprehensive tests for unified state management."""

    @pytest.fixture
    def sample_state(self):
        """Create a sample unified state for testing."""
        return create_unified_state(
            user_id="test_user_123",
            conversation_id="conv_456",
            workflow_type="chat_message",
            initial_message={
                "content": "Hello, I need help with marketing analysis",
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
        )

    def test_create_unified_state(self):
        """Test unified state creation."""
        state = create_unified_state(
            user_id="user123",
            conversation_id="conv456",
            workflow_type="test_workflow"
        )
        
        # Verify required fields
        assert state["user_id"] == "user123"
        assert state["conversation_id"] == "conv456"
        assert state["workflow_type"] == "test_workflow"
        assert "workflow_id" in state
        assert "created_at" in state
        assert "updated_at" in state
        
        # Verify default values
        assert state["workflow_status"] == WorkflowStatus.INITIALIZING
        assert state["current_agent"] == "concierge_agent"
        assert isinstance(state["messages"], list)
        assert isinstance(state["shared_insights"], list)
        assert isinstance(state["active_agents"], set)
        assert isinstance(state["agent_contributions"], dict)

    def test_create_unified_state_with_initial_message(self):
        """Test unified state creation with initial message."""
        initial_message = {
            "content": "Test message",
            "type": "user",
            "timestamp": datetime.now().isoformat()
        }
        
        state = create_unified_state(
            user_id="user123",
            conversation_id="conv456",
            workflow_type="chat_message",
            initial_message=initial_message
        )
        
        assert len(state["messages"]) == 1
        assert state["messages"][0] == initial_message

    def test_update_workflow_status(self, sample_state):
        """Test workflow status updates."""
        # Update to active status
        updated_state = update_workflow_status(
            sample_state,
            WorkflowStatus.ACTIVE,
            phase="agent_execution"
        )
        
        assert updated_state["workflow_status"] == WorkflowStatus.ACTIVE
        assert updated_state["workflow_phase"] == "agent_execution"
        assert len(updated_state["step_history"]) > 0
        
        # Verify step history entry
        last_step = updated_state["step_history"][-1]
        assert last_step["new_status"] == WorkflowStatus.ACTIVE.value
        assert last_step["phase"] == "agent_execution"
        assert "timestamp" in last_step

    def test_add_cross_agent_insight(self, sample_state):
        """Test adding cross-agent insights."""
        insight_data = {
            "content": "User is interested in marketing analytics",
            "confidence": 0.85,
            "metadata": {"source": "intent_analysis"}
        }
        
        updated_state = add_cross_agent_insight(
            state=sample_state,
            insight=insight_data,
            source_agent="concierge_agent",
            target_agents=["marketing_agent"],
            insight_type="user_intent",
            priority=2,
            relevance_score=0.9
        )
        
        # Verify insight was added
        assert len(updated_state["shared_insights"]) == 1
        insight = updated_state["shared_insights"][0]
        
        assert insight["content"] == insight_data["content"]
        assert insight["source_agent"] == "concierge_agent"
        assert insight["target_agents"] == ["marketing_agent"]
        assert insight["insight_type"] == "user_intent"
        assert insight["priority"] == 2
        assert insight["relevance_score"] == 0.9
        assert "id" in insight
        assert "timestamp" in insight

    def test_get_relevant_insights(self, sample_state):
        """Test retrieving relevant insights for an agent."""
        # Add multiple insights
        insights_data = [
            {
                "content": "Marketing insight 1",
                "source_agent": "concierge_agent",
                "insight_type": "marketing_strategy",
                "priority": 3,
                "relevance_score": 0.9
            },
            {
                "content": "Analysis insight 1",
                "source_agent": "analysis_agent",
                "insight_type": "data_analysis",
                "priority": 2,
                "relevance_score": 0.7
            },
            {
                "content": "Low relevance insight",
                "source_agent": "concierge_agent",
                "insight_type": "general",
                "priority": 1,
                "relevance_score": 0.3
            }
        ]
        
        # Add insights to state
        for insight_data in insights_data:
            sample_state = add_cross_agent_insight(
                state=sample_state,
                insight=insight_data,
                source_agent=insight_data["source_agent"],
                insight_type=insight_data["insight_type"],
                priority=insight_data["priority"],
                relevance_score=insight_data["relevance_score"]
            )
        
        # Get relevant insights for marketing agent
        relevant_insights = get_relevant_insights(
            state=sample_state,
            target_agent="marketing_agent",
            insight_types=["marketing_strategy", "data_analysis"],
            min_priority=2,
            min_relevance=0.6,
            max_insights=5
        )
        
        # Should get 2 insights (excluding low relevance and self-generated)
        assert len(relevant_insights) == 2
        
        # Verify insights are sorted by priority and relevance
        assert relevant_insights[0]["priority"] >= relevant_insights[1]["priority"]

    def test_build_agent_consensus(self, sample_state):
        """Test building consensus among agents."""
        decision_id = "test_decision_123"
        decision_data = {
            "topic": "Choose marketing strategy",
            "options": ["email_campaign", "social_media", "content_marketing"]
        }
        participating_agents = ["marketing_agent", "analysis_agent", "concierge_agent"]
        
        updated_state = build_agent_consensus(
            state=sample_state,
            decision_id=decision_id,
            decision_data=decision_data,
            participating_agents=participating_agents,
            consensus_threshold=0.7
        )
        
        # Verify decision was added
        assert len(updated_state["pending_decisions"]) == 1
        decision = updated_state["pending_decisions"][0]
        
        assert decision["id"] == decision_id
        assert decision["data"] == decision_data
        assert decision["participating_agents"] == participating_agents
        assert decision["consensus_threshold"] == 0.7
        assert decision["status"] == "pending"
        
        # Verify agent votes were initialized
        for agent in participating_agents:
            assert agent in updated_state["agent_votes"]
            assert decision_id in updated_state["agent_votes"][agent]
            assert updated_state["agent_votes"][agent][decision_id]["vote"] is None

    def test_record_agent_vote(self, sample_state):
        """Test recording agent votes for consensus."""
        decision_id = "test_decision_123"
        
        # First build consensus
        sample_state = build_agent_consensus(
            state=sample_state,
            decision_id=decision_id,
            decision_data={"topic": "test"},
            participating_agents=["agent1", "agent2", "agent3"],
            consensus_threshold=0.7
        )
        
        # Record votes
        sample_state = record_agent_vote(
            state=sample_state,
            decision_id=decision_id,
            agent_id="agent1",
            vote="option_a",
            confidence=0.9,
            reasoning="Best option based on analysis"
        )
        
        sample_state = record_agent_vote(
            state=sample_state,
            decision_id=decision_id,
            agent_id="agent2",
            vote="option_a",
            confidence=0.8,
            reasoning="Agrees with agent1"
        )
        
        sample_state = record_agent_vote(
            state=sample_state,
            decision_id=decision_id,
            agent_id="agent3",
            vote="option_a",
            confidence=0.7,
            reasoning="Consensus choice"
        )
        
        # Verify votes were recorded
        assert sample_state["agent_votes"]["agent1"][decision_id]["vote"] == "option_a"
        assert sample_state["agent_votes"]["agent1"][decision_id]["confidence"] == 0.9
        
        # Verify consensus was reached (all 3 agents voted, threshold was 0.7)
        decision = sample_state["pending_decisions"][0]
        assert decision["status"] == "consensus_reached"
        assert "consensus_result" in decision

    def test_update_agent_transition(self, sample_state):
        """Test agent transitions."""
        # Transition to marketing agent
        updated_state = update_agent_transition(
            state=sample_state,
            new_agent="marketing_agent",
            role=AgentRole.PRIMARY
        )
        
        assert updated_state["current_agent"] == "marketing_agent"
        assert "marketing_agent" in updated_state["active_agents"]
        assert updated_state["participating_agents"]["marketing_agent"] == AgentRole.PRIMARY
        
        # Verify transition was logged
        assert len(updated_state["agent_transitions"]) > 0
        last_transition = updated_state["agent_transitions"][-1]
        assert last_transition["to_agent"] == "marketing_agent"
        assert last_transition["role"] == AgentRole.PRIMARY.value

    @patch('agents.langgraph.states.unified_state.get_db')
    @patch('agents.langgraph.states.unified_state.BusinessProfileService')
    def test_load_business_profile_context(self, mock_service_class, mock_get_db, sample_state):
        """Test loading business profile context."""
        # Setup mocks
        mock_db = Mock()
        mock_get_db.return_value = iter([mock_db])
        
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        mock_profile = Mock()
        mock_profile.model_dump.return_value = {
            "id": "profile_123",
            "business_name": "Test Business",
            "industry": "Technology",
            "business_description": "A test business"
        }
        mock_service.get_business_profile.return_value = mock_profile
        mock_service.get_profile_data_sources.return_value = []
        
        # Load business context
        updated_state = load_business_profile_context(
            state=sample_state,
            business_profile_id="profile_123",
            include_data_sources=True,
            include_insights=True
        )
        
        # Verify context was loaded
        assert updated_state["business_profile_id"] == "profile_123"
        assert "business_context" in updated_state
        assert "profile_data" in updated_state["business_context"]
        assert updated_state["business_context"]["profile_data"]["business_name"] == "Test Business"

    def test_state_immutability_protection(self, sample_state):
        """Test that state updates don't mutate original state."""
        original_status = sample_state["workflow_status"]
        original_messages_count = len(sample_state["messages"])
        
        # Update state
        updated_state = update_workflow_status(
            sample_state,
            WorkflowStatus.ACTIVE
        )
        
        # Original state should be unchanged
        assert sample_state["workflow_status"] == original_status
        assert len(sample_state["messages"]) == original_messages_count
        
        # Updated state should have changes
        assert updated_state["workflow_status"] == WorkflowStatus.ACTIVE

    def test_state_validation(self):
        """Test state validation for required fields."""
        # Test with missing required fields
        with pytest.raises((KeyError, ValueError)):
            create_unified_state(
                user_id="",  # Empty user_id should fail
                conversation_id="conv123",
                workflow_type="test"
            )

    def test_cross_agent_context_management(self, sample_state):
        """Test cross-agent context management."""
        # Add insight and verify cross-agent context is updated
        insight_data = {"content": "Test insight"}
        
        updated_state = add_cross_agent_insight(
            state=sample_state,
            insight=insight_data,
            source_agent="test_agent",
            insight_type="test_type",
            priority=2
        )
        
        # Verify cross-agent context was updated
        assert "recent_insights" in updated_state["cross_agent_context"]
        recent_insights = updated_state["cross_agent_context"]["recent_insights"]
        assert len(recent_insights) == 1
        assert recent_insights[0]["source_agent"] == "test_agent"
        assert recent_insights[0]["insight_type"] == "test_type"
