"""
Core MCP Tool Migration for LangGraph System.

This module handles the migration of essential MCP tools to LangGraph
tool execution nodes, ensuring all existing functionality is preserved.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .mcp_integration import MCPToolRegistry, MCPToolExecutionNode
from ..states.unified_state import UnifiedDatageniusState, update_tool_status, ToolStatus

logger = logging.getLogger(__name__)


class CoreToolMigrator:
    """
    Migrates core MCP tools to LangGraph-compatible implementations.
    
    This migrator handles the conversion of essential tools while
    maintaining their existing interfaces and capabilities.
    """
    
    def __init__(self, tool_registry: MCPToolRegistry):
        """
        Initialize the core tool migrator.
        
        Args:
            tool_registry: MCP tool registry instance
        """
        self.tool_registry = tool_registry
        self.logger = logging.getLogger(__name__)
        
        # Core tools that need special handling
        self.core_tools = {
            "data_access": "DataAccessTool",
            "pandasai_analysis": "PandasAIAnalysisTool", 
            "pandasai_visualization": "PandasAIVisualizationTool",
            "text_processing": "TextProcessingTool",
            "sentiment_analysis": "SentimentAnalysisTool",
            "text_classification": "TextClassificationTool",
            "statistical_analysis": "StatisticalAnalysisTool",
            "data_visualization": "DataVisualizationTool",
            "marketing_strategy_generation": "MarketingStrategyGenerationTool",
            "code_execution_tool": "CodeExecutionTool",
            "conversation_tool": "ConversationTool",
            "intent_detection": "IntentDetectionTool",
            "language_detection": "LanguageDetectionTool"
        }
        
        # Tool categories for organization
        self.tool_categories = {
            "data_analysis": ["data_access", "pandasai_analysis", "statistical_analysis"],
            "data_visualization": ["pandasai_visualization", "data_visualization"],
            "text_processing": ["text_processing", "sentiment_analysis", "text_classification"],
            "marketing": ["marketing_strategy_generation"],
            "conversation": ["conversation_tool", "intent_detection", "language_detection"],
            "execution": ["code_execution_tool"]
        }
        
        self.logger.info("CoreToolMigrator initialized")
    
    async def migrate_all_core_tools(self) -> Dict[str, Any]:
        """
        Migrate all core MCP tools to LangGraph implementations.
        
        Returns:
            Migration results summary
        """
        try:
            self.logger.info("Starting core MCP tool migration")
            
            migration_results = {
                "migrated_tools": [],
                "failed_tools": [],
                "total_tools": len(self.core_tools),
                "migration_timestamp": datetime.now().isoformat()
            }
            
            # Migrate each core tool
            for tool_name, tool_class_name in self.core_tools.items():
                try:
                    result = await self._migrate_single_tool(tool_name, tool_class_name)
                    if result["success"]:
                        migration_results["migrated_tools"].append(result)
                        self.logger.info(f"Successfully migrated: {tool_name}")
                    else:
                        migration_results["failed_tools"].append(result)
                        self.logger.error(f"Failed to migrate: {tool_name}")
                        
                except Exception as e:
                    self.logger.error(f"Error migrating tool {tool_name}: {e}")
                    migration_results["failed_tools"].append({
                        "tool_name": tool_name,
                        "error": str(e),
                        "success": False
                    })
            
            # Generate summary
            migration_results["success_count"] = len(migration_results["migrated_tools"])
            migration_results["failure_count"] = len(migration_results["failed_tools"])
            migration_results["success_rate"] = (
                migration_results["success_count"] / migration_results["total_tools"] * 100
                if migration_results["total_tools"] > 0 else 0
            )
            
            self.logger.info(f"Core tool migration complete: {migration_results['success_count']}/{migration_results['total_tools']} tools migrated")
            
            return migration_results
            
        except Exception as e:
            self.logger.error(f"Error in core tool migration: {e}", exc_info=True)
            return {"error": str(e), "migration_timestamp": datetime.now().isoformat()}
    
    async def _migrate_single_tool(self, tool_name: str, tool_class_name: str) -> Dict[str, Any]:
        """
        Migrate a single MCP tool to LangGraph implementation.
        
        Args:
            tool_name: Name of the tool
            tool_class_name: Class name of the tool
            
        Returns:
            Migration result
        """
        try:
            # Check if tool is already registered
            if tool_name in self.tool_registry.tools:
                return {
                    "tool_name": tool_name,
                    "status": "already_registered",
                    "success": True,
                    "message": "Tool already registered in LangGraph system"
                }
            
            # Import and create tool instance
            tool_instance = await self._create_tool_instance(tool_class_name)
            if not tool_instance:
                return {
                    "tool_name": tool_name,
                    "status": "creation_failed",
                    "success": False,
                    "error": f"Could not create instance of {tool_class_name}"
                }
            
            # Create enhanced LangGraph tool node
            enhanced_tool_node = await self._create_enhanced_tool_node(tool_instance, tool_name)
            
            # Register with tool registry
            self.tool_registry.register_tool(tool_name, enhanced_tool_node, {
                "class_name": tool_class_name,
                "category": self._get_tool_category(tool_name),
                "migration_timestamp": datetime.now().isoformat(),
                "enhanced": True,
                "core_tool": True
            })
            
            return {
                "tool_name": tool_name,
                "class_name": tool_class_name,
                "category": self._get_tool_category(tool_name),
                "status": "migrated",
                "success": True,
                "capabilities": self._extract_tool_capabilities(tool_instance),
                "migration_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error migrating tool {tool_name}: {e}")
            return {
                "tool_name": tool_name,
                "status": "migration_failed",
                "success": False,
                "error": str(e)
            }
    
    async def _create_tool_instance(self, tool_class_name: str) -> Optional[Any]:
        """
        Create an instance of the specified tool class.
        
        Args:
            tool_class_name: Name of the tool class
            
        Returns:
            Tool instance or None if creation failed
        """
        try:
            # Import from existing MCP tools
            from ...tools.mcp import AVAILABLE_TOOLS
            
            if tool_class_name in AVAILABLE_TOOLS:
                tool_class = AVAILABLE_TOOLS[tool_class_name]
                return tool_class()
            else:
                self.logger.error(f"Tool class {tool_class_name} not found in AVAILABLE_TOOLS")
                return None
                
        except Exception as e:
            self.logger.error(f"Error creating tool instance {tool_class_name}: {e}")
            return None
    
    async def _create_enhanced_tool_node(self, tool_instance: Any, tool_name: str) -> MCPToolExecutionNode:
        """
        Create an enhanced LangGraph tool node with additional capabilities.
        
        Args:
            tool_instance: Original tool instance
            tool_name: Name of the tool
            
        Returns:
            Enhanced MCPToolExecutionNode
        """
        # Create server wrapper with enhanced capabilities
        class EnhancedToolServerWrapper:
            def __init__(self, tool, tool_name):
                self.tool = tool
                self.tool_name = tool_name
                self.execution_count = 0
                self.success_count = 0
                self.error_count = 0
                self.total_execution_time = 0.0
            
            async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
                """Call the wrapped tool with enhanced error handling and monitoring."""
                start_time = datetime.now()
                self.execution_count += 1
                
                try:
                    # Execute the tool
                    result = await self.tool.execute(arguments)
                    
                    # Track success
                    self.success_count += 1
                    execution_time = (datetime.now() - start_time).total_seconds()
                    self.total_execution_time += execution_time
                    
                    # Enhance result with metadata
                    if isinstance(result, dict):
                        result["execution_metadata"] = {
                            "tool_name": self.tool_name,
                            "execution_time": execution_time,
                            "timestamp": datetime.now().isoformat(),
                            "success": True
                        }
                    
                    return result
                    
                except Exception as e:
                    # Track error
                    self.error_count += 1
                    execution_time = (datetime.now() - start_time).total_seconds()
                    self.total_execution_time += execution_time
                    
                    logger.error(f"Error executing tool {self.tool_name}: {e}")
                    
                    # Return error result
                    return {
                        "content": [{
                            "type": "text",
                            "text": f"Error executing {self.tool_name}: {str(e)}"
                        }],
                        "execution_metadata": {
                            "tool_name": self.tool_name,
                            "execution_time": execution_time,
                            "timestamp": datetime.now().isoformat(),
                            "success": False,
                            "error": str(e)
                        }
                    }
            
            def get_tools(self) -> List[Dict[str, Any]]:
                """Get tool definitions."""
                return [self.tool.definition]
            
            def get_performance_metrics(self) -> Dict[str, Any]:
                """Get performance metrics for the tool."""
                return {
                    "execution_count": self.execution_count,
                    "success_count": self.success_count,
                    "error_count": self.error_count,
                    "success_rate": self.success_count / max(self.execution_count, 1),
                    "average_execution_time": self.total_execution_time / max(self.execution_count, 1),
                    "total_execution_time": self.total_execution_time
                }
        
        # Create enhanced server wrapper
        enhanced_server = EnhancedToolServerWrapper(tool_instance, tool_name)
        
        # Create enhanced tool node
        enhanced_config = {
            "description": tool_instance.description,
            "input_schema": getattr(tool_instance, "input_schema", {}),
            "source": "core_tool_migration",
            "enhanced": True,
            "performance_monitoring": True,
            "error_handling": True
        }
        
        return MCPToolExecutionNode(
            tool_name=tool_name,
            mcp_server=enhanced_server,
            tool_config=enhanced_config
        )
    
    def _get_tool_category(self, tool_name: str) -> str:
        """
        Get the category for a tool.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool category
        """
        for category, tools in self.tool_categories.items():
            if tool_name in tools:
                return category
        return "general"
    
    def _extract_tool_capabilities(self, tool_instance: Any) -> List[str]:
        """
        Extract capabilities from a tool instance.
        
        Args:
            tool_instance: Tool instance to analyze
            
        Returns:
            List of capabilities
        """
        capabilities = []
        
        # Extract from tool name and description
        tool_name = tool_instance.name.lower()
        description = tool_instance.description.lower()
        
        # Capability patterns
        capability_patterns = {
            "data_analysis": ["data", "analysis", "statistical", "pandas"],
            "text_processing": ["text", "language", "nlp", "sentiment"],
            "visualization": ["chart", "graph", "plot", "visual"],
            "classification": ["classify", "categorize", "label"],
            "generation": ["generate", "create", "produce"],
            "extraction": ["extract", "parse", "retrieve"],
            "conversation": ["conversation", "chat", "dialogue"],
            "intent": ["intent", "purpose", "goal"],
            "marketing": ["marketing", "campaign", "content"],
            "code": ["code", "execution", "programming"]
        }
        
        for capability, patterns in capability_patterns.items():
            if any(pattern in tool_name or pattern in description for pattern in patterns):
                capabilities.append(capability)
        
        return capabilities if capabilities else ["general"]
    
    def get_migration_status(self) -> Dict[str, Any]:
        """
        Get the current migration status.
        
        Returns:
            Migration status information
        """
        registered_core_tools = []
        missing_core_tools = []
        
        for tool_name in self.core_tools.keys():
            if tool_name in self.tool_registry.tools:
                registered_core_tools.append(tool_name)
            else:
                missing_core_tools.append(tool_name)
        
        return {
            "total_core_tools": len(self.core_tools),
            "registered_tools": len(registered_core_tools),
            "missing_tools": len(missing_core_tools),
            "registration_rate": len(registered_core_tools) / len(self.core_tools) * 100,
            "registered_tool_names": registered_core_tools,
            "missing_tool_names": missing_core_tools,
            "tool_categories": self.tool_categories
        }


# Global migrator instance
core_tool_migrator = None

def get_core_tool_migrator(tool_registry: MCPToolRegistry) -> CoreToolMigrator:
    """Get or create the global core tool migrator."""
    global core_tool_migrator
    if core_tool_migrator is None:
        core_tool_migrator = CoreToolMigrator(tool_registry)
    return core_tool_migrator
