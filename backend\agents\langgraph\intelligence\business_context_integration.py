"""
Business Profile Context Integration for LangGraph Agents.

This module provides automatic business profile context loading and sharing
across all agents with proper scoping and access controls.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..states.unified_state import (
    UnifiedDatageniusState,
    load_business_profile_context,
    share_business_context_across_agents,
    add_cross_agent_insight
)

logger = logging.getLogger(__name__)


class ContextScope(str, Enum):
    """Scopes for business context access."""
    FULL = "full"  # Full access to all business data
    BASIC = "basic"  # Basic profile information only
    RESTRICTED = "restricted"  # Limited access based on agent role
    READ_ONLY = "read_only"  # Read-only access


class ContextUpdateType(str, Enum):
    """Types of business context updates."""
    PROFILE_CHANGE = "profile_change"
    DATA_SOURCE_UPDATE = "data_source_update"
    INSIGHT_ADDITION = "insight_addition"
    METADATA_UPDATE = "metadata_update"
    ACCESS_CHANGE = "access_change"


@dataclass
class BusinessContextAccess:
    """Access control for business context."""
    agent_id: str
    scope: ContextScope
    allowed_fields: Set[str]
    restricted_fields: Set[str]
    expiry_time: Optional[datetime] = None


@dataclass
class ContextUpdate:
    """Business context update notification."""
    update_type: ContextUpdateType
    business_profile_id: str
    updated_fields: List[str]
    source_agent: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None


class BusinessContextIntegrationService:
    """
    Service for integrating business profile context across all LangGraph agents.
    
    Provides:
    - Automatic context loading when business profile changes
    - Scoped access control for different agent types
    - Real-time context updates across active agents
    - Context caching and optimization
    - Privacy and security controls
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the business context integration service.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Configuration parameters
        self.context_cache_ttl_minutes = self.config.get("context_cache_ttl_minutes", 30)
        self.auto_refresh_enabled = self.config.get("auto_refresh_enabled", True)
        self.max_context_size_kb = self.config.get("max_context_size_kb", 100)
        
        # Agent access control mappings
        self.agent_access_levels = {
            "concierge_agent": ContextScope.FULL,
            "marketing_agent": ContextScope.FULL,
            "analysis_agent": ContextScope.BASIC,
            "classification_agent": ContextScope.BASIC
        }
        
        # Field access control by scope
        self.scope_field_access = {
            ContextScope.FULL: {
                "allowed": {"*"},  # All fields
                "restricted": set()
            },
            ContextScope.BASIC: {
                "allowed": {
                    "id", "name", "description", "industry", "business_type",
                    "business_size", "target_audience", "products_services"
                },
                "restricted": {
                    "budget_indicators", "competitive_landscape", "context_metadata"
                }
            },
            ContextScope.RESTRICTED: {
                "allowed": {
                    "id", "name", "industry", "business_type", "target_audience"
                },
                "restricted": {
                    "description", "products_services", "marketing_goals",
                    "budget_indicators", "competitive_landscape", "context_metadata"
                }
            },
            ContextScope.READ_ONLY: {
                "allowed": {"id", "name", "industry"},
                "restricted": {"*"}  # All other fields restricted
            }
        }
    
    async def load_and_distribute_context(
        self,
        state: UnifiedDatageniusState,
        business_profile_id: str,
        force_refresh: bool = False
    ) -> UnifiedDatageniusState:
        """
        Load business profile context and distribute to all active agents.
        
        Args:
            state: Current workflow state
            business_profile_id: Business profile identifier
            force_refresh: Force refresh even if context is cached
            
        Returns:
            Updated state with business context loaded and distributed
        """
        try:
            # Check if context is already loaded and fresh
            if not force_refresh and self._is_context_fresh(state, business_profile_id):
                self.logger.debug(f"Business context for {business_profile_id} is already fresh")
                return state
            
            # Load business profile context
            updated_state = load_business_profile_context(
                state=state,
                business_profile_id=business_profile_id,
                include_data_sources=True,
                include_insights=True
            )
            
            # Apply access controls for active agents
            updated_state = await self._apply_access_controls(updated_state)
            
            # Distribute context to active agents
            if updated_state["active_agents"]:
                context_update = {
                    "business_profile_loaded": True,
                    "profile_id": business_profile_id,
                    "loaded_at": datetime.now().isoformat(),
                    "context_version": updated_state["context_version"],
                    "available_agents": list(updated_state["active_agents"])
                }
                
                updated_state = share_business_context_across_agents(
                    state=updated_state,
                    context_update=context_update,
                    source_agent="business_context_integration"
                )
            
            # Create context loading insight
            context_insight = {
                "content": {
                    "action": "business_context_loaded",
                    "profile_id": business_profile_id,
                    "context_size": len(str(updated_state["business_context"])),
                    "active_agents": list(updated_state["active_agents"])
                },
                "confidence": 1.0,
                "metadata": {
                    "context_version": updated_state["context_version"],
                    "load_timestamp": datetime.now().isoformat()
                }
            }
            
            updated_state = add_cross_agent_insight(
                state=updated_state,
                insight=context_insight,
                source_agent="business_context_integration",
                insight_type="business_context_update",
                priority=2,
                relevance_score=1.0
            )
            
            self.logger.info(f"Loaded and distributed business context for profile {business_profile_id}")
            
            return updated_state
            
        except Exception as e:
            self.logger.error(f"Error loading and distributing business context: {e}")
            state["error_history"].append({
                "timestamp": datetime.now().isoformat(),
                "source": "business_context_integration",
                "error": str(e),
                "context": {"business_profile_id": business_profile_id}
            })
            return state
    
    async def update_context_for_agents(
        self,
        state: UnifiedDatageniusState,
        context_update: ContextUpdate
    ) -> UnifiedDatageniusState:
        """
        Update business context for all relevant agents.
        
        Args:
            state: Current workflow state
            context_update: Context update information
            
        Returns:
            Updated state with context changes propagated
        """
        try:
            # Validate business profile scope
            if state["business_profile_id"] != context_update.business_profile_id:
                self.logger.warning(
                    f"Context update for different profile: "
                    f"current={state['business_profile_id']}, update={context_update.business_profile_id}"
                )
                return state
            
            # Create update notification for agents
            update_notification = {
                "update_type": context_update.update_type.value,
                "updated_fields": context_update.updated_fields,
                "source_agent": context_update.source_agent,
                "timestamp": context_update.timestamp.isoformat(),
                "metadata": context_update.metadata or {}
            }
            
            # Share update across agents
            updated_state = share_business_context_across_agents(
                state=state,
                context_update=update_notification,
                source_agent=context_update.source_agent
            )
            
            # Create insight about the context update
            update_insight = {
                "content": {
                    "action": "business_context_updated",
                    "update_type": context_update.update_type.value,
                    "updated_fields": context_update.updated_fields,
                    "profile_id": context_update.business_profile_id
                },
                "confidence": 1.0,
                "metadata": context_update.metadata or {}
            }
            
            updated_state = add_cross_agent_insight(
                state=updated_state,
                insight=update_insight,
                source_agent=context_update.source_agent,
                insight_type="business_context_update",
                priority=2,
                relevance_score=0.8
            )
            
            self.logger.info(
                f"Updated business context: {context_update.update_type.value} "
                f"for profile {context_update.business_profile_id}"
            )
            
            return updated_state
            
        except Exception as e:
            self.logger.error(f"Error updating context for agents: {e}")
            state["error_history"].append({
                "timestamp": datetime.now().isoformat(),
                "source": "business_context_integration",
                "error": str(e),
                "context": {"update_type": context_update.update_type.value}
            })
            return state
    
    async def get_agent_context_access(
        self,
        state: UnifiedDatageniusState,
        agent_id: str
    ) -> BusinessContextAccess:
        """
        Get context access permissions for a specific agent.
        
        Args:
            state: Current workflow state
            agent_id: Agent identifier
            
        Returns:
            Context access permissions for the agent
        """
        # Get agent's access level
        access_level = self.agent_access_levels.get(agent_id, ContextScope.RESTRICTED)
        
        # Get field access permissions
        field_permissions = self.scope_field_access.get(access_level, {
            "allowed": {"id", "name"},
            "restricted": {"*"}
        })
        
        return BusinessContextAccess(
            agent_id=agent_id,
            scope=access_level,
            allowed_fields=field_permissions["allowed"],
            restricted_fields=field_permissions["restricted"]
        )
    
    async def filter_context_for_agent(
        self,
        business_context: Dict[str, Any],
        agent_access: BusinessContextAccess
    ) -> Dict[str, Any]:
        """
        Filter business context based on agent access permissions.
        
        Args:
            business_context: Full business context
            agent_access: Agent access permissions
            
        Returns:
            Filtered business context for the agent
        """
        if agent_access.scope == ContextScope.FULL:
            return business_context
        
        filtered_context = {}
        
        # Filter profile data
        if "profile_data" in business_context:
            profile_data = business_context["profile_data"]
            filtered_profile = {}
            
            for field, value in profile_data.items():
                # Check if field is allowed
                if ("*" in agent_access.allowed_fields or 
                    field in agent_access.allowed_fields):
                    # Check if field is not restricted
                    if ("*" not in agent_access.restricted_fields and 
                        field not in agent_access.restricted_fields):
                        filtered_profile[field] = value
            
            filtered_context["profile_data"] = filtered_profile
        
        # Always include basic metadata
        for key in ["loaded_at", "context_version"]:
            if key in business_context:
                filtered_context[key] = business_context[key]
        
        # Filter data sources based on access level
        if (agent_access.scope in [ContextScope.FULL, ContextScope.BASIC] and 
            "data_sources" in business_context):
            filtered_context["data_sources"] = business_context["data_sources"]
        
        return filtered_context
    
    def _is_context_fresh(
        self,
        state: UnifiedDatageniusState,
        business_profile_id: str
    ) -> bool:
        """Check if business context is fresh and doesn't need refresh."""
        business_context = state.get("business_context", {})
        
        # Check if context exists for the right profile
        if not business_context or state.get("business_profile_id") != business_profile_id:
            return False
        
        # Check if context is within TTL
        loaded_at_str = business_context.get("loaded_at")
        if not loaded_at_str:
            return False
        
        try:
            loaded_at = datetime.fromisoformat(loaded_at_str)
            ttl_threshold = datetime.now() - timedelta(minutes=self.context_cache_ttl_minutes)
            return loaded_at > ttl_threshold
        except (ValueError, TypeError):
            return False
    
    async def _apply_access_controls(
        self,
        state: UnifiedDatageniusState
    ) -> UnifiedDatageniusState:
        """Apply access controls to business context for active agents."""
        try:
            # Store agent-specific filtered contexts
            if "agent_contexts" not in state["cross_agent_context"]:
                state["cross_agent_context"]["agent_contexts"] = {}
            
            business_context = state.get("business_context", {})
            
            for agent_id in state["active_agents"]:
                # Get agent access permissions
                agent_access = await self.get_agent_context_access(state, agent_id)
                
                # Filter context for this agent
                filtered_context = await self.filter_context_for_agent(
                    business_context, agent_access
                )
                
                # Store filtered context
                state["cross_agent_context"]["agent_contexts"][agent_id] = {
                    "context": filtered_context,
                    "access_level": agent_access.scope.value,
                    "filtered_at": datetime.now().isoformat()
                }
            
            return state
            
        except Exception as e:
            self.logger.error(f"Error applying access controls: {e}")
            return state
