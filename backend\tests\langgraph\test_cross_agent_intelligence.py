"""
Comprehensive tests for LangGraph Cross-Agent Intelligence System.

This module provides comprehensive unit tests for the cross-agent intelligence
system including insight sharing, collaboration workflows, and analytics.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

# Import cross-agent intelligence components
from agents.langgraph.intelligence import (
    CrossAgentIntelligenceService,
    IntelligenceAnalytics,
    BusinessContextIntegrationService,
    CollaborationWorkflowEngine,
    CollaborationType,
    InsightType,
    IntelligenceInsight,
    CollaborationRequest,
    WorkflowPattern,
    CollaborationWorkflow,
    WorkflowStep
)
from agents.langgraph.states.unified_state import create_unified_state


class TestCrossAgentIntelligenceService:
    """Tests for CrossAgentIntelligenceService."""

    @pytest.fixture
    def intelligence_service(self):
        """Create a CrossAgentIntelligenceService instance."""
        return CrossAgentIntelligenceService()

    @pytest.fixture
    def sample_state(self):
        """Create a sample unified state."""
        return create_unified_state(
            user_id="test_user",
            conversation_id="test_conv",
            workflow_type="intelligence_test"
        )

    @pytest.fixture
    def sample_insight(self):
        """Create a sample intelligence insight."""
        return IntelligenceInsight(
            content={"analysis": "User needs marketing help", "confidence": 0.85},
            insight_type=InsightType.USER_INTENT,
            source_agent="concierge_agent",
            target_agents=["marketing_agent"],
            priority=2,
            relevance_score=0.9,
            confidence=0.85
        )

    @pytest.mark.asyncio
    async def test_share_insight(self, intelligence_service, sample_state, sample_insight):
        """Test sharing intelligence insights."""
        updated_state = await intelligence_service.share_insight(
            state=sample_state,
            insight=sample_insight
        )
        
        # Verify insight was added
        assert len(updated_state["shared_insights"]) == 1
        shared_insight = updated_state["shared_insights"][0]
        
        assert shared_insight["content"] == sample_insight.content
        assert shared_insight["source_agent"] == sample_insight.source_agent
        assert shared_insight["insight_type"] == sample_insight.insight_type.value
        assert shared_insight["priority"] == sample_insight.priority

    @pytest.mark.asyncio
    async def test_get_agent_insights(self, intelligence_service, sample_state):
        """Test retrieving insights for specific agents."""
        # Add multiple insights
        insights = [
            IntelligenceInsight(
                content={"data": "marketing insight"},
                insight_type=InsightType.MARKETING_STRATEGY,
                source_agent="analysis_agent",
                priority=3,
                relevance_score=0.9
            ),
            IntelligenceInsight(
                content={"data": "data insight"},
                insight_type=InsightType.DATA_ANALYSIS,
                source_agent="concierge_agent",
                priority=2,
                relevance_score=0.8
            ),
            IntelligenceInsight(
                content={"data": "low relevance"},
                insight_type=InsightType.GENERAL,
                source_agent="concierge_agent",
                priority=1,
                relevance_score=0.3
            )
        ]
        
        # Share insights
        for insight in insights:
            sample_state = await intelligence_service.share_insight(sample_state, insight)
        
        # Get insights for marketing agent
        retrieved_insights = await intelligence_service.get_agent_insights(
            state=sample_state,
            target_agent="marketing_agent",
            insight_types=[InsightType.MARKETING_STRATEGY, InsightType.DATA_ANALYSIS],
            max_insights=5
        )
        
        # Should get 2 relevant insights (excluding low relevance)
        assert len(retrieved_insights) == 2

    @pytest.mark.asyncio
    async def test_initiate_collaboration(self, intelligence_service, sample_state):
        """Test initiating agent collaboration."""
        collaboration_request = CollaborationRequest(
            requesting_agent="concierge_agent",
            target_agents=["marketing_agent", "analysis_agent"],
            collaboration_type=CollaborationType.CONSENSUS,
            task_description="Analyze user's marketing needs",
            priority=2,
            timeout_minutes=30,
            required_consensus=0.7
        )
        
        updated_state = await intelligence_service.initiate_collaboration(
            state=sample_state,
            collaboration_request=collaboration_request
        )
        
        # Verify collaboration was initiated
        assert len(updated_state["pending_decisions"]) == 1
        assert updated_state["collaboration_mode"] == CollaborationType.CONSENSUS.value
        
        # Verify participating agents were set
        assert "marketing_agent" in updated_state["participating_agents"]
        assert "analysis_agent" in updated_state["participating_agents"]

    @pytest.mark.asyncio
    async def test_load_business_context(self, intelligence_service, sample_state):
        """Test loading business context."""
        with patch('agents.langgraph.intelligence.cross_agent_intelligence.load_business_profile_context') as mock_load:
            mock_load.return_value = sample_state
            
            updated_state = await intelligence_service.load_business_context(
                state=sample_state,
                business_profile_id="profile_123"
            )
            
            # Verify context loading was called
            mock_load.assert_called_once_with(
                state=sample_state,
                business_profile_id="profile_123",
                include_data_sources=True,
                include_insights=True
            )

    @pytest.mark.asyncio
    async def test_get_collaboration_opportunities(self, intelligence_service, sample_state):
        """Test getting collaboration opportunities."""
        # Setup agent capabilities in state
        sample_state["agent_capabilities"] = {
            "concierge_agent": ["guidance", "coordination"],
            "marketing_agent": ["marketing", "strategy"],
            "analysis_agent": ["data_analysis", "visualization"]
        }
        
        sample_state["active_agents"] = {"marketing_agent", "analysis_agent"}
        
        opportunities = await intelligence_service.get_collaboration_opportunities(
            state=sample_state,
            current_agent="concierge_agent"
        )
        
        # Should find opportunities with other active agents
        assert len(opportunities) == 2
        
        # Verify opportunity structure
        for opp in opportunities:
            assert "agent_id" in opp
            assert "complementary_capabilities" in opp
            assert "collaboration_type" in opp
            assert "potential_value" in opp


class TestIntelligenceAnalytics:
    """Tests for IntelligenceAnalytics."""

    @pytest.fixture
    def analytics_service(self):
        """Create an IntelligenceAnalytics instance."""
        return IntelligenceAnalytics()

    @pytest.fixture
    def sample_state_with_data(self):
        """Create a sample state with analytics data."""
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conv",
            workflow_type="analytics_test"
        )
        
        # Add sample decisions and insights
        state["pending_decisions"] = [
            {
                "id": "decision_1",
                "created_at": datetime.now().isoformat(),
                "status": "consensus_reached",
                "completed_at": datetime.now().isoformat(),
                "participating_agents": ["agent1", "agent2"],
                "data": {"collaboration_type": "consensus"}
            }
        ]
        
        state["shared_insights"] = [
            {
                "id": "insight_1",
                "timestamp": datetime.now().isoformat(),
                "source_agent": "agent1",
                "insight_type": "marketing_strategy",
                "relevance_score": 0.9,
                "target_agents": ["agent2"]
            },
            {
                "id": "insight_2",
                "timestamp": datetime.now().isoformat(),
                "source_agent": "agent2",
                "insight_type": "data_analysis",
                "relevance_score": 0.8,
                "target_agents": ["agent1"]
            }
        ]
        
        return state

    def test_analyze_collaboration_effectiveness(self, analytics_service, sample_state_with_data):
        """Test collaboration effectiveness analysis."""
        metrics = analytics_service.analyze_collaboration_effectiveness(
            state=sample_state_with_data,
            time_window_hours=24
        )
        
        assert metrics.total_collaborations == 1
        assert metrics.successful_collaborations == 1
        assert metrics.consensus_rate == 1.0
        assert len(metrics.agent_participation_rate) > 0
        assert "consensus" in metrics.collaboration_types

    def test_analyze_insight_sharing_patterns(self, analytics_service, sample_state_with_data):
        """Test insight sharing pattern analysis."""
        metrics = analytics_service.analyze_insight_sharing_patterns(
            state=sample_state_with_data,
            time_window_hours=24
        )
        
        assert metrics.total_insights_shared == 2
        assert len(metrics.insights_by_type) > 0
        assert len(metrics.insights_by_agent) > 0
        assert metrics.average_relevance_score > 0
        assert metrics.insight_utilization_rate > 0

    def test_analyze_business_context_utilization(self, analytics_service, sample_state_with_data):
        """Test business context utilization analysis."""
        # Add business context data
        sample_state_with_data["business_context"] = {
            "loaded_at": datetime.now().isoformat(),
            "updates": [
                {
                    "timestamp": datetime.now().isoformat(),
                    "source_agent": "agent1"
                }
            ]
        }
        sample_state_with_data["business_profile_id"] = "profile_123"
        
        metrics = analytics_service.analyze_business_context_utilization(
            state=sample_state_with_data,
            time_window_hours=24
        )
        
        assert metrics.profiles_accessed == 1
        assert metrics.context_updates_shared == 1
        assert len(metrics.context_utilization_by_agent) > 0

    def test_generate_intelligence_report(self, analytics_service, sample_state_with_data):
        """Test comprehensive intelligence report generation."""
        report = analytics_service.generate_intelligence_report(
            state=sample_state_with_data,
            time_window_hours=24
        )
        
        assert "report_generated_at" in report
        assert "collaboration_metrics" in report
        assert "insight_sharing_metrics" in report
        assert "business_context_metrics" in report
        assert "recommendations" in report
        assert "summary" in report
        
        # Verify summary contains key metrics
        summary = report["summary"]
        assert "total_collaborations" in summary
        assert "total_insights_shared" in summary
        assert "overall_effectiveness_score" in summary


class TestBusinessContextIntegrationService:
    """Tests for BusinessContextIntegrationService."""

    @pytest.fixture
    def context_service(self):
        """Create a BusinessContextIntegrationService instance."""
        return BusinessContextIntegrationService()

    @pytest.fixture
    def sample_state(self):
        """Create a sample unified state."""
        return create_unified_state(
            user_id="test_user",
            conversation_id="test_conv",
            workflow_type="context_test"
        )

    @pytest.mark.asyncio
    async def test_load_and_distribute_context(self, context_service, sample_state):
        """Test loading and distributing business context."""
        with patch('agents.langgraph.intelligence.business_context_integration.load_business_profile_context') as mock_load:
            mock_load.return_value = sample_state
            
            updated_state = await context_service.load_and_distribute_context(
                state=sample_state,
                business_profile_id="profile_123"
            )
            
            # Verify context loading was called
            mock_load.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_agent_context_access(self, context_service, sample_state):
        """Test getting agent context access permissions."""
        access = await context_service.get_agent_context_access(
            state=sample_state,
            agent_id="marketing_agent"
        )
        
        assert access.agent_id == "marketing_agent"
        assert access.scope is not None
        assert isinstance(access.allowed_fields, set)
        assert isinstance(access.restricted_fields, set)

    @pytest.mark.asyncio
    async def test_filter_context_for_agent(self, context_service):
        """Test filtering business context based on agent permissions."""
        business_context = {
            "profile_data": {
                "id": "profile_123",
                "name": "Test Business",
                "description": "Sensitive business info",
                "industry": "Technology"
            },
            "loaded_at": datetime.now().isoformat()
        }
        
        # Create access with restricted fields
        from agents.langgraph.intelligence.business_context_integration import (
            BusinessContextAccess, ContextScope
        )
        
        access = BusinessContextAccess(
            agent_id="test_agent",
            scope=ContextScope.BASIC,
            allowed_fields={"id", "name", "industry"},
            restricted_fields={"description"}
        )
        
        filtered_context = await context_service.filter_context_for_agent(
            business_context=business_context,
            agent_access=access
        )
        
        # Verify filtering
        assert "profile_data" in filtered_context
        profile_data = filtered_context["profile_data"]
        assert "id" in profile_data
        assert "name" in profile_data
        assert "industry" in profile_data
        assert "description" not in profile_data  # Should be filtered out


class TestCollaborationWorkflowEngine:
    """Tests for CollaborationWorkflowEngine."""

    @pytest.fixture
    def workflow_engine(self):
        """Create a CollaborationWorkflowEngine instance."""
        return CollaborationWorkflowEngine()

    @pytest.fixture
    def sample_workflow(self):
        """Create a sample collaboration workflow."""
        steps = [
            WorkflowStep(
                step_id="step_1",
                agent_id="concierge_agent",
                task_description="Analyze user request",
                required_capabilities=["analysis"],
                dependencies=[],
                estimated_duration_minutes=5
            ),
            WorkflowStep(
                step_id="step_2",
                agent_id="marketing_agent",
                task_description="Create marketing strategy",
                required_capabilities=["marketing"],
                dependencies=["step_1"],
                estimated_duration_minutes=10
            )
        ]
        
        return CollaborationWorkflow(
            workflow_id="test_workflow_123",
            name="Marketing Analysis Workflow",
            description="Analyze user needs and create marketing strategy",
            pattern=WorkflowPattern.SEQUENTIAL,
            steps=steps,
            handoff_criteria=[],
            timeout_minutes=30
        )

    @pytest.fixture
    def sample_state(self):
        """Create a sample unified state."""
        return create_unified_state(
            user_id="test_user",
            conversation_id="test_conv",
            workflow_type="collaboration_test"
        )

    @pytest.mark.asyncio
    async def test_execute_workflow(self, workflow_engine, sample_state, sample_workflow):
        """Test executing a collaboration workflow."""
        updated_state = await workflow_engine.execute_workflow(
            state=sample_state,
            workflow=sample_workflow
        )
        
        # Verify workflow execution
        assert updated_state["collaboration_mode"] == WorkflowPattern.SEQUENTIAL.value
        assert "workflow_plan" in updated_state
        
        workflow_plan = updated_state["workflow_plan"]
        assert workflow_plan["workflow_id"] == sample_workflow.workflow_id
        assert workflow_plan["name"] == sample_workflow.name
        assert workflow_plan["total_steps"] == len(sample_workflow.steps)

    @pytest.mark.asyncio
    async def test_build_consensus_decision(self, workflow_engine, sample_state):
        """Test building consensus decisions."""
        updated_state = await workflow_engine.build_consensus_decision(
            state=sample_state,
            decision_topic="Choose marketing approach",
            participating_agents=["marketing_agent", "analysis_agent"],
            decision_data={"options": ["email", "social", "content"]},
            consensus_threshold=0.7
        )
        
        # Verify consensus building
        assert len(updated_state["pending_decisions"]) == 1
        decision = updated_state["pending_decisions"][0]
        assert decision["data"]["topic"] == "Choose marketing approach"
        assert len(decision["participating_agents"]) == 2
