"""
Migration validation tests for LangGraph system.

This module validates functionality parity between the legacy orchestrator
system and the new LangGraph implementation.
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from typing import Dict, Any, List

# Import both legacy and LangGraph components for comparison
from agents.langgraph.core.workflow_manager import WorkflowManager
from agents.langgraph.states.unified_state import create_unified_state
from agents.langgraph.intelligence import CrossAgentIntelligenceService


class TestMigrationParity:
    """Tests to validate functionality parity after migration."""

    @pytest.fixture
    def langgraph_workflow_manager(self):
        """Create LangGraph workflow manager."""
        return WorkflowManager()

    @pytest.fixture
    def intelligence_service(self):
        """Create cross-agent intelligence service."""
        return CrossAgentIntelligenceService()

    @pytest.fixture
    def sample_user_request(self):
        """Create a sample user request for testing."""
        return {
            "user_id": "validation_user_123",
            "conversation_id": "validation_conv_456",
            "message": "I need help creating a marketing strategy for my tech startup",
            "context": {
                "business_profile_id": "profile_123",
                "user_preferences": {"communication_style": "professional"}
            }
        }

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_basic_chat_functionality_parity(self, langgraph_workflow_manager, sample_user_request):
        """Test that basic chat functionality works the same as legacy system."""
        # Create LangGraph state
        langgraph_state = create_unified_state(
            user_id=sample_user_request["user_id"],
            conversation_id=sample_user_request["conversation_id"],
            workflow_type="chat_message",
            initial_message={
                "content": sample_user_request["message"],
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
        )
        
        # Mock agent execution for LangGraph
        with patch.object(langgraph_workflow_manager, '_execute_agent_node') as mock_execute:
            mock_response_state = langgraph_state.copy()
            mock_response_state["messages"].append({
                "content": "I'd be happy to help you create a marketing strategy for your tech startup.",
                "type": "agent",
                "agent_id": "marketing_agent",
                "timestamp": datetime.now().isoformat()
            })
            mock_execute.return_value = mock_response_state
            
            # Execute LangGraph workflow
            langgraph_result = await langgraph_workflow_manager.execute_workflow(
                langgraph_state["workflow_id"]
            )
            
            # Validate LangGraph results
            assert langgraph_result is not None
            assert "messages" in langgraph_result
            assert len(langgraph_result["messages"]) >= 2  # User + agent message
            
            # Verify agent response exists
            agent_messages = [msg for msg in langgraph_result["messages"] if msg.get("type") == "agent"]
            assert len(agent_messages) >= 1
            assert "marketing strategy" in agent_messages[0]["content"].lower()

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_agent_routing_parity(self, langgraph_workflow_manager):
        """Test that agent routing works consistently."""
        test_cases = [
            {
                "message": "I need help with marketing strategy",
                "expected_agent": "marketing_agent"
            },
            {
                "message": "Can you analyze this data for me?",
                "expected_agent": "analysis_agent"
            },
            {
                "message": "Classify this document",
                "expected_agent": "classification_agent"
            },
            {
                "message": "Hello, how are you?",
                "expected_agent": "concierge_agent"
            }
        ]
        
        for test_case in test_cases:
            # Create state for routing test
            state = create_unified_state(
                user_id="routing_test_user",
                conversation_id="routing_test_conv",
                workflow_type="routing_test",
                initial_message={
                    "content": test_case["message"],
                    "type": "user",
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            # Test LangGraph routing
            routed_agent = langgraph_workflow_manager._route_to_agent(state)
            
            # Validate routing
            assert routed_agent == test_case["expected_agent"], \
                f"Message '{test_case['message']}' should route to {test_case['expected_agent']}, got {routed_agent}"

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_cross_agent_intelligence_parity(self, intelligence_service):
        """Test that cross-agent intelligence works as expected."""
        # Create initial state
        state = create_unified_state(
            user_id="intelligence_test_user",
            conversation_id="intelligence_test_conv",
            workflow_type="intelligence_test"
        )
        
        # Test insight sharing
        from agents.langgraph.intelligence import IntelligenceInsight, InsightType
        
        insight = IntelligenceInsight(
            content={"user_intent": "marketing_help", "confidence": 0.9},
            insight_type=InsightType.USER_INTENT,
            source_agent="concierge_agent",
            target_agents=["marketing_agent"],
            priority=2,
            relevance_score=0.9
        )
        
        # Share insight
        updated_state = await intelligence_service.share_insight(state, insight)
        
        # Validate insight sharing
        assert len(updated_state["shared_insights"]) == 1
        shared_insight = updated_state["shared_insights"][0]
        assert shared_insight["source_agent"] == "concierge_agent"
        assert shared_insight["insight_type"] == InsightType.USER_INTENT.value
        
        # Test insight retrieval
        retrieved_insights = await intelligence_service.get_agent_insights(
            state=updated_state,
            target_agent="marketing_agent",
            insight_types=[InsightType.USER_INTENT],
            max_insights=5
        )
        
        # Validate insight retrieval
        assert len(retrieved_insights) == 1
        assert retrieved_insights[0]["content"]["user_intent"] == "marketing_help"

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_streaming_functionality_parity(self, langgraph_workflow_manager):
        """Test that streaming functionality works correctly."""
        # Create state for streaming test
        state = create_unified_state(
            user_id="streaming_test_user",
            conversation_id="streaming_test_conv",
            workflow_type="streaming_test",
            initial_message={
                "content": "Generate a detailed response",
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
        )
        
        # Mock streaming execution
        with patch.object(langgraph_workflow_manager, '_route_to_agent', return_value="concierge_agent"):
            stream_events = []
            
            # Collect streaming events
            async for event in langgraph_workflow_manager.execute_workflow_streaming(
                state=state,
                workflow_type="streaming_test"
            ):
                stream_events.append(event)
                
                # Break after reasonable number of events
                if len(stream_events) >= 5:
                    break
            
            # Validate streaming
            assert len(stream_events) > 0
            
            # Check for expected event types
            event_types = {event["type"] for event in stream_events}
            assert "workflow_started" in event_types
            
            # Verify events have required fields
            for event in stream_events:
                assert "type" in event
                assert "timestamp" in event

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_performance_comparison(self, langgraph_workflow_manager):
        """Test that LangGraph performance meets or exceeds legacy system."""
        # Create multiple test states
        num_tests = 5
        states = []
        
        for i in range(num_tests):
            state = create_unified_state(
                user_id=f"perf_test_user_{i}",
                conversation_id=f"perf_test_conv_{i}",
                workflow_type="performance_test",
                initial_message={
                    "content": f"Performance test message {i}",
                    "type": "user",
                    "timestamp": datetime.now().isoformat()
                }
            )
            states.append(state)
        
        # Mock agent execution
        with patch.object(langgraph_workflow_manager, '_execute_agent_node') as mock_execute:
            mock_execute.side_effect = states
            
            # Measure LangGraph performance
            start_time = time.time()
            
            tasks = [
                langgraph_workflow_manager.execute_workflow(state["workflow_id"])
                for state in states
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            langgraph_duration = end_time - start_time
            
            # Validate performance
            assert all(not isinstance(r, Exception) for r in results)
            assert langgraph_duration < 5.0  # Should complete within 5 seconds
            
            # Calculate average response time
            avg_response_time = langgraph_duration / num_tests
            assert avg_response_time < 1.0  # Average should be under 1 second per request

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_error_handling_parity(self, langgraph_workflow_manager):
        """Test that error handling works consistently."""
        # Create state that will trigger error
        error_state = create_unified_state(
            user_id="error_test_user",
            conversation_id="error_test_conv",
            workflow_type="error_test"
        )
        
        # Mock agent execution to raise error
        with patch.object(langgraph_workflow_manager, '_execute_agent_node') as mock_execute:
            mock_execute.side_effect = Exception("Simulated error")
            
            # Execute workflow (should handle error gracefully)
            result = await langgraph_workflow_manager.execute_workflow(error_state["workflow_id"])
            
            # Validate error handling
            assert result is None  # Should return None on error
            
            # Verify error doesn't crash the system
            assert langgraph_workflow_manager is not None

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_state_management_consistency(self, langgraph_workflow_manager):
        """Test that state management is consistent and reliable."""
        # Create initial state
        initial_state = create_unified_state(
            user_id="state_test_user",
            conversation_id="state_test_conv",
            workflow_type="state_test"
        )
        
        # Verify initial state structure
        required_fields = [
            "workflow_id", "user_id", "conversation_id", "workflow_type",
            "created_at", "updated_at", "workflow_status", "current_agent",
            "messages", "shared_insights", "active_agents"
        ]
        
        for field in required_fields:
            assert field in initial_state, f"Required field '{field}' missing from state"
        
        # Test state updates
        from agents.langgraph.states.unified_state import update_workflow_status, WorkflowStatus
        
        updated_state = update_workflow_status(
            initial_state,
            WorkflowStatus.ACTIVE,
            phase="testing"
        )
        
        # Verify state update
        assert updated_state["workflow_status"] == WorkflowStatus.ACTIVE
        assert updated_state["workflow_phase"] == "testing"
        assert len(updated_state["step_history"]) > 0
        
        # Verify original state wasn't mutated
        assert initial_state["workflow_status"] != WorkflowStatus.ACTIVE

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_api_compatibility(self, langgraph_workflow_manager, sample_user_request):
        """Test that API interfaces remain compatible."""
        # Test workflow manager interface
        assert hasattr(langgraph_workflow_manager, 'execute_workflow')
        assert hasattr(langgraph_workflow_manager, 'execute_workflow_streaming')
        assert hasattr(langgraph_workflow_manager, 'get_metrics')
        
        # Test method signatures are callable
        workflow_id = "test_workflow_123"
        
        # These should not raise exceptions about method signatures
        try:
            await langgraph_workflow_manager.execute_workflow(workflow_id)
        except Exception as e:
            # Should fail due to missing workflow, not signature issues
            assert "not found" in str(e).lower() or "invalid" in str(e).lower()
        
        # Test metrics interface
        metrics = langgraph_workflow_manager.get_metrics()
        assert isinstance(metrics, dict)

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_memory_usage_validation(self, langgraph_workflow_manager, memory_profiler):
        """Test that memory usage is reasonable."""
        memory_profiler.start()
        
        # Create and execute multiple workflows
        num_workflows = 10
        states = []
        
        for i in range(num_workflows):
            state = create_unified_state(
                user_id=f"memory_test_user_{i}",
                conversation_id=f"memory_test_conv_{i}",
                workflow_type="memory_test"
            )
            states.append(state)
            memory_profiler.update()
        
        # Mock execution
        with patch.object(langgraph_workflow_manager, '_execute_agent_node') as mock_execute:
            mock_execute.side_effect = states
            
            # Execute workflows
            tasks = [
                langgraph_workflow_manager.execute_workflow(state["workflow_id"])
                for state in states
            ]
            
            await asyncio.gather(*tasks, return_exceptions=True)
            memory_profiler.update()
        
        # Validate memory usage
        memory_increase = memory_profiler.memory_increase
        if memory_increase:
            # Memory increase should be reasonable (less than 100MB for 10 workflows)
            assert memory_increase < 100 * 1024 * 1024, f"Memory increase too high: {memory_increase} bytes"

    def test_configuration_compatibility(self):
        """Test that configuration interfaces remain compatible."""
        # Test that LangGraph components can be instantiated with default config
        workflow_manager = WorkflowManager()
        assert workflow_manager is not None
        
        intelligence_service = CrossAgentIntelligenceService()
        assert intelligence_service is not None
        
        # Test configuration with custom config
        custom_config = {
            "max_workflows": 100,
            "timeout_seconds": 300,
            "enable_streaming": True
        }
        
        configured_manager = WorkflowManager(config=custom_config)
        assert configured_manager is not None

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_concurrent_execution_stability(self, langgraph_workflow_manager):
        """Test system stability under concurrent load."""
        # Create many concurrent workflows
        num_concurrent = 20
        states = []
        
        for i in range(num_concurrent):
            state = create_unified_state(
                user_id=f"concurrent_user_{i}",
                conversation_id=f"concurrent_conv_{i}",
                workflow_type="concurrent_test"
            )
            states.append(state)
        
        # Mock execution
        with patch.object(langgraph_workflow_manager, '_execute_agent_node') as mock_execute:
            mock_execute.side_effect = states
            
            # Execute all workflows concurrently
            tasks = [
                langgraph_workflow_manager.execute_workflow(state["workflow_id"])
                for state in states
            ]
            
            # This should not raise any exceptions or deadlocks
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Validate all completed successfully
            successful_results = [r for r in results if not isinstance(r, Exception)]
            assert len(successful_results) == num_concurrent
