"""
End-to-end integration tests for LangGraph workflows.

This module tests complete user workflows from initial request through
agent processing to final response delivery.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from typing import Dict, Any

# Import components for integration testing
from agents.langgraph.core.workflow_manager import WorkflowManager
from agents.langgraph.states.unified_state import create_unified_state
from agents.langgraph.intelligence import CrossAgentIntelligenceService
from agents.langgraph.api.websocket_manager import WebSocketManager


class TestEndToEndWorkflows:
    """End-to-end integration tests for complete user workflows."""

    @pytest.fixture
    async def integration_setup(self):
        """Setup integration test environment."""
        # Create real instances (with mocked dependencies)
        workflow_manager = WorkflowManager()
        intelligence_service = CrossAgentIntelligenceService()
        websocket_manager = WebSocketManager()
        
        # Mock external dependencies
        with patch('agents.langgraph.core.workflow_manager.get_db'):
            with patch('agents.langgraph.intelligence.cross_agent_intelligence.get_db'):
                yield {
                    "workflow_manager": workflow_manager,
                    "intelligence_service": intelligence_service,
                    "websocket_manager": websocket_manager
                }

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_complete_chat_workflow(self, integration_setup):
        """Test complete chat workflow from user message to agent response."""
        components = integration_setup
        workflow_manager = components["workflow_manager"]
        
        # Create initial state for user message
        initial_state = create_unified_state(
            user_id="integration_user_123",
            conversation_id="integration_conv_456",
            workflow_type="chat_message",
            initial_message={
                "content": "I need help creating a marketing strategy for my tech startup",
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
        )
        
        # Mock agent execution
        with patch.object(workflow_manager, '_execute_agent_node') as mock_execute:
            mock_execute.return_value = initial_state.copy()
            mock_execute.return_value["messages"].append({
                "content": "I'd be happy to help you create a marketing strategy for your tech startup. Let me analyze your needs and provide recommendations.",
                "type": "agent",
                "agent_id": "marketing_agent",
                "timestamp": datetime.now().isoformat()
            })
            
            # Execute workflow
            result = await workflow_manager.execute_workflow(initial_state["workflow_id"])
            
            # Verify workflow completion
            assert result is not None
            assert "messages" in result
            assert len(result["messages"]) >= 2  # User message + agent response
            
            # Verify agent response
            agent_messages = [msg for msg in result["messages"] if msg.get("type") == "agent"]
            assert len(agent_messages) >= 1
            assert "marketing strategy" in agent_messages[0]["content"].lower()

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_multi_agent_collaboration_workflow(self, integration_setup):
        """Test multi-agent collaboration workflow."""
        components = integration_setup
        workflow_manager = components["workflow_manager"]
        intelligence_service = components["intelligence_service"]
        
        # Create initial state
        initial_state = create_unified_state(
            user_id="integration_user_123",
            conversation_id="integration_conv_456",
            workflow_type="multi_agent_collaboration",
            initial_message={
                "content": "I need a comprehensive analysis of my business data and marketing recommendations",
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
        )
        
        # Mock multi-agent execution
        with patch.object(workflow_manager, '_execute_agent_node') as mock_execute:
            # Simulate concierge agent routing
            concierge_state = initial_state.copy()
            concierge_state["current_agent"] = "concierge_agent"
            concierge_state["active_agents"] = {"concierge_agent", "analysis_agent", "marketing_agent"}
            
            # Simulate analysis agent processing
            analysis_state = concierge_state.copy()
            analysis_state["current_agent"] = "analysis_agent"
            analysis_state["messages"].append({
                "content": "I've analyzed your business data and found key insights about customer behavior and market trends.",
                "type": "agent",
                "agent_id": "analysis_agent",
                "timestamp": datetime.now().isoformat()
            })
            
            # Simulate marketing agent processing
            marketing_state = analysis_state.copy()
            marketing_state["current_agent"] = "marketing_agent"
            marketing_state["messages"].append({
                "content": "Based on the analysis, I recommend a multi-channel marketing approach focusing on digital channels.",
                "type": "agent",
                "agent_id": "marketing_agent",
                "timestamp": datetime.now().isoformat()
            })
            
            mock_execute.side_effect = [concierge_state, analysis_state, marketing_state]
            
            # Execute workflow
            result = await workflow_manager.execute_workflow(initial_state["workflow_id"])
            
            # Verify multi-agent collaboration
            assert result is not None
            assert len(result["active_agents"]) >= 2
            
            # Verify both agents contributed
            agent_messages = [msg for msg in result["messages"] if msg.get("type") == "agent"]
            agent_ids = {msg["agent_id"] for msg in agent_messages}
            assert "analysis_agent" in agent_ids
            assert "marketing_agent" in agent_ids

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_streaming_workflow_integration(self, integration_setup):
        """Test streaming workflow integration."""
        components = integration_setup
        workflow_manager = components["workflow_manager"]
        
        # Create initial state
        initial_state = create_unified_state(
            user_id="integration_user_123",
            conversation_id="integration_conv_456",
            workflow_type="streaming_chat",
            initial_message={
                "content": "Generate a detailed marketing plan for my business",
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
        )
        
        # Mock streaming execution
        with patch.object(workflow_manager, '_route_to_agent', return_value="marketing_agent"):
            stream_events = []
            
            # Collect streaming events
            async for event in workflow_manager.execute_workflow_streaming(
                state=initial_state,
                workflow_type="streaming_chat"
            ):
                stream_events.append(event)
                
                # Break after reasonable number of events for testing
                if len(stream_events) >= 10:
                    break
            
            # Verify streaming events
            assert len(stream_events) > 0
            
            # Check for expected event types
            event_types = {event["type"] for event in stream_events}
            assert "workflow_started" in event_types
            
            # Verify content streaming
            content_events = [e for e in stream_events if e["type"] == "content"]
            if content_events:
                assert len(content_events) > 0
                assert all("content" in event for event in content_events)

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_cross_agent_intelligence_integration(self, integration_setup):
        """Test cross-agent intelligence sharing integration."""
        components = integration_setup
        intelligence_service = components["intelligence_service"]
        
        # Create initial state
        initial_state = create_unified_state(
            user_id="integration_user_123",
            conversation_id="integration_conv_456",
            workflow_type="intelligence_sharing"
        )
        
        # Test insight sharing workflow
        from agents.langgraph.intelligence import IntelligenceInsight, InsightType
        
        # Agent 1 shares insight
        insight1 = IntelligenceInsight(
            content={"user_intent": "marketing_help", "confidence": 0.9},
            insight_type=InsightType.USER_INTENT,
            source_agent="concierge_agent",
            target_agents=["marketing_agent"],
            priority=2,
            relevance_score=0.9
        )
        
        state_after_insight1 = await intelligence_service.share_insight(
            state=initial_state,
            insight=insight1
        )
        
        # Agent 2 retrieves relevant insights
        relevant_insights = await intelligence_service.get_agent_insights(
            state=state_after_insight1,
            target_agent="marketing_agent",
            insight_types=[InsightType.USER_INTENT],
            max_insights=5
        )
        
        # Verify intelligence sharing
        assert len(relevant_insights) == 1
        assert relevant_insights[0]["content"]["user_intent"] == "marketing_help"
        assert relevant_insights[0]["source_agent"] == "concierge_agent"
        
        # Agent 2 shares insight back
        insight2 = IntelligenceInsight(
            content={"strategy_recommendation": "focus_on_digital", "confidence": 0.8},
            insight_type=InsightType.MARKETING_STRATEGY,
            source_agent="marketing_agent",
            target_agents=["concierge_agent"],
            priority=2,
            relevance_score=0.8
        )
        
        final_state = await intelligence_service.share_insight(
            state=state_after_insight1,
            insight=insight2
        )
        
        # Verify bidirectional intelligence sharing
        assert len(final_state["shared_insights"]) == 2
        
        # Verify cross-agent context was updated
        assert "recent_insights" in final_state["cross_agent_context"]
        recent_insights = final_state["cross_agent_context"]["recent_insights"]
        assert len(recent_insights) == 2

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_business_context_integration_workflow(self, integration_setup):
        """Test business context integration workflow."""
        components = integration_setup
        intelligence_service = components["intelligence_service"]
        
        # Create initial state
        initial_state = create_unified_state(
            user_id="integration_user_123",
            conversation_id="integration_conv_456",
            workflow_type="business_context_integration"
        )
        
        # Mock business profile loading
        with patch('agents.langgraph.intelligence.cross_agent_intelligence.load_business_profile_context') as mock_load:
            mock_business_context = {
                "profile_data": {
                    "id": "profile_123",
                    "business_name": "Integration Test Business",
                    "industry": "Technology",
                    "business_description": "A test technology company"
                },
                "loaded_at": datetime.now().isoformat()
            }
            
            mock_state_with_context = initial_state.copy()
            mock_state_with_context["business_context"] = mock_business_context
            mock_state_with_context["business_profile_id"] = "profile_123"
            
            mock_load.return_value = mock_state_with_context
            
            # Load business context
            updated_state = await intelligence_service.load_business_context(
                state=initial_state,
                business_profile_id="profile_123"
            )
            
            # Verify business context integration
            assert "business_context" in updated_state
            assert updated_state["business_profile_id"] == "profile_123"
            
            # Verify context was shared across agents
            assert len(updated_state["shared_insights"]) > 0
            context_insights = [
                insight for insight in updated_state["shared_insights"]
                if insight.get("insight_type") == "business_context_update"
            ]
            assert len(context_insights) > 0

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_error_handling_and_recovery_workflow(self, integration_setup):
        """Test error handling and recovery in workflows."""
        components = integration_setup
        workflow_manager = components["workflow_manager"]
        
        # Create initial state
        initial_state = create_unified_state(
            user_id="integration_user_123",
            conversation_id="integration_conv_456",
            workflow_type="error_handling_test"
        )
        
        # Mock agent execution that raises an error
        with patch.object(workflow_manager, '_execute_agent_node') as mock_execute:
            mock_execute.side_effect = Exception("Simulated agent error")
            
            # Execute workflow (should handle error gracefully)
            result = await workflow_manager.execute_workflow(initial_state["workflow_id"])
            
            # Verify error handling
            assert result is None  # Workflow should return None on error
            
            # Verify workflow was not cached due to error
            assert initial_state["workflow_id"] not in workflow_manager.workflow_cache

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_concurrent_workflow_execution_integration(self, integration_setup):
        """Test concurrent execution of multiple workflows."""
        components = integration_setup
        workflow_manager = components["workflow_manager"]
        
        # Create multiple initial states
        states = []
        for i in range(3):
            state = create_unified_state(
                user_id=f"integration_user_{i}",
                conversation_id=f"integration_conv_{i}",
                workflow_type="concurrent_test",
                initial_message={
                    "content": f"Test message {i}",
                    "type": "user",
                    "timestamp": datetime.now().isoformat()
                }
            )
            states.append(state)
        
        # Mock agent execution
        with patch.object(workflow_manager, '_execute_agent_node') as mock_execute:
            mock_execute.side_effect = states  # Return corresponding state for each execution
            
            # Execute workflows concurrently
            tasks = [
                workflow_manager.execute_workflow(state["workflow_id"])
                for state in states
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Verify all workflows completed successfully
            assert len(results) == 3
            for i, result in enumerate(results):
                assert not isinstance(result, Exception)
                assert result is not None
                assert result["user_id"] == f"integration_user_{i}"

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_performance_under_load(self, integration_setup, performance_timer):
        """Test system performance under load."""
        components = integration_setup
        workflow_manager = components["workflow_manager"]
        
        # Create multiple workflows for load testing
        num_workflows = 10
        states = []
        
        for i in range(num_workflows):
            state = create_unified_state(
                user_id=f"load_test_user_{i}",
                conversation_id=f"load_test_conv_{i}",
                workflow_type="load_test"
            )
            states.append(state)
        
        # Mock agent execution
        with patch.object(workflow_manager, '_execute_agent_node') as mock_execute:
            mock_execute.side_effect = states
            
            # Measure execution time
            performance_timer.start()
            
            # Execute workflows concurrently
            tasks = [
                workflow_manager.execute_workflow(state["workflow_id"])
                for state in states
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            performance_timer.stop()
            
            # Verify performance
            assert len(results) == num_workflows
            assert all(not isinstance(r, Exception) for r in results)
            
            # Performance assertion (adjust threshold as needed)
            assert performance_timer.elapsed < 10.0  # Should complete within 10 seconds
