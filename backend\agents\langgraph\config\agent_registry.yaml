# Unified Persona Registry Configuration
# This file defines all available personas and their configurations

personas:
  concierge:
    agent_type: "concierge"
    name: "Datagenius Concierge"
    description: "General purpose agent for greetings, routing, and persona recommendations"
    agent_class: "agents.concierge_agent.concierge.ConciergeAgent"
    capabilities:
      - "persona_recommendation"
      - "intent_analysis"
      - "conversation_management"
      - "data_attachment_assistance"
      - "workflow_coordination"
      - "user_guidance"
      - "business_context_awareness"
    supported_intents:
      - "persona_request"
      - "general_inquiry"
      - "data_attachment"
      - "workflow_coordination"
      - "help_request"
      - "greeting"
    tools:
      - "persona_recommender"
      - "data_attachment_assistant"
      - "context_manager"
      - "conversation_state_manager"
    priority: 1
    fallback: true

  marketing:
    agent_type: "marketing"
    name: "Composable Marketing AI"
    description: "Specialized agent for marketing content creation and strategy"
    agent_class: "agents.marketing_agent.composable_agent.ComposableMarketingAgent"
    capabilities:
      - "content_generation"
      - "campaign_creation"
      - "social_media_management"
      - "brand_strategy"
      - "marketing_analysis"
      - "audience_targeting"
      - "performance_tracking"
      - "competitive_analysis"
    supported_intents:
      - "content_creation"
      - "campaign_planning"
      - "social_media_post"
      - "marketing_strategy"
      - "brand_development"
      - "audience_analysis"
      - "performance_review"
    tools:
      - "content_generator"
      - "campaign_planner"
      - "social_media_manager"
      - "brand_analyzer"
      - "audience_segmenter"
    priority: 2

  analysis:
    agent_type: "analysis"
    name: "Composable Analysis AI"
    description: "Specialized agent for data analysis and visualization"
    agent_class: "agents.analysis_agent.composable_agent.ComposableAnalysisAgent"
    capabilities:
      - "data_analysis"
      - "statistical_analysis"
      - "data_visualization"
      - "chart_generation"
      - "report_creation"
      - "trend_analysis"
      - "pattern_recognition"
      - "predictive_modeling"
      - "dashboard_creation"
      - "data_cleaning"
      - "exploratory_data_analysis"
    supported_intents:
      - "data_analysis"
      - "visualization_request"
      - "statistical_analysis"
      - "report_generation"
      - "trend_analysis"
      - "data_exploration"
      - "chart_creation"
      - "dashboard_request"
    tools:
      - "data_analyzer"
      - "chart_generator"
      - "report_generator"
      - "pandasai_query"
      - "statistical_analyzer"
      - "trend_detector"
      - "data_cleaner"
      - "visualization_engine"
    priority: 2

  composable-classifier-ai:
    agent_type: "classification"
    name: "Composable Classifier AI"
    description: "Specialized agent for content classification and categorization"
    agent_class: "agents.classification.composable_agent.ComposableClassificationAgent"
    capabilities:
      - "content_classification"
      - "text_categorization"
      - "sentiment_analysis"
      - "topic_modeling"
      - "entity_recognition"
      - "document_processing"
      - "data_labeling"
      - "pattern_matching"
    supported_intents:
      - "classification_request"
      - "categorization"
      - "sentiment_analysis"
      - "topic_extraction"
      - "entity_extraction"
      - "document_analysis"
      - "data_labeling"
    tools:
      - "text_classifier"
      - "sentiment_analyzer"
      - "topic_modeler"
      - "entity_extractor"
      - "document_processor"
      - "pattern_matcher"
    priority: 2

# Default configuration for new agents
default_agent_config:
  agent_type: "general"
  capabilities: []
  supported_intents: ["general_inquiry"]
  tools: []
  priority: 3
  fallback: false
  agent_init_config: {}

# Agent discovery settings
discovery:
  enabled: true
  paths:
    - "agents.langgraph.agents"
    - "agents.concierge_agent"
    - "agents.marketing_agent"
    - "agents.analysis_agent"
    - "agents.classification"
    - "agents.custom"
  auto_register: true
  scan_interval: 300  # seconds

# Dynamic loading configuration
dynamic_loading:
  enabled: true
  hot_reload: true
  config_watch: true
  fallback_agents:
    - "concierge"
